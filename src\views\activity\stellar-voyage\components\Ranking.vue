<template>
  <div>
    <GoldBox>
      <div class="box-title">
        <TitleBlock
          :title="$t('stellar_voyage.starfield_rankings')"
        ></TitleBlock>
      </div>
      <div class="box-desc">
        {{ $t("stellar_voyage.ranking_rule") }}
      </div>
      <div class="ranking">
        <div class="top3">
          <div class="top-2">
            <div class="room-cover">
              <img
                v-if="top2"
                :src="top2?.cover"
                @click="goVoiceRoom(top2?.room_id)"
                v-track:click
                trace-key="room_join_click"
                :track-params="
                  JSON.stringify({
                    room_join_click_source: '90',
                  })
                "
              />
              <div v-else class="empty-avatar">
                <svg-icon icon="vector" :size="20"></svg-icon>
              </div>
              <div class="room-top2-cover"></div>
            </div>
            <div class="nickname">{{ top2?.name || "-" }}</div>
            <div class="coin-box">
              <div class="pk-value-icon"></div>
              <gap :gap="3"></gap>
              <div class="coin-number">{{ formatCount(top2?.coin ?? 0) }}</div>
            </div>
            <div
              class="top-users"
              @click="contributionPopupRef.open(top2)"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '5',
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              <div class="top-users-1">
                <div class="top-users-inner">
                  <img :src="(top2?.top || [])[0]?.avatar" />
                  <div class="tag">1</div>
                </div>
              </div>
              <div class="top-users-2">
                <div class="top-users-inner">
                  <img :src="(top2?.top || [])[1]?.avatar" />
                  <div class="tag">2</div>
                </div>
              </div>
              <div class="top-users-3">
                <div class="top-users-inner">
                  <img :src="(top2?.top || [])[2]?.avatar" />
                  <div class="tag">3</div>
                </div>
              </div>
              <div class="top-users-more"></div>
            </div>
          </div>
          <div class="top-1">
            <div class="room-cover">
              <img
                v-if="top1"
                :src="top1?.cover"
                @click="goVoiceRoom(top1?.room_id)"
                v-track:click
                trace-key="room_join_click"
                :track-params="
                  JSON.stringify({
                    room_join_click_source: '90',
                  })
                "
              />
              <div v-else class="empty-avatar">
                <svg-icon icon="vector" :size="20"></svg-icon>
              </div>
              <div class="room-top1-cover"></div>
            </div>
            <div class="nickname">{{ top1?.name || "-" }}</div>
            <div class="coin-box">
              <div class="pk-value-icon"></div>
              <gap :gap="3"></gap>
              <div class="coin-number">{{ formatCount(top1?.coin ?? 0) }}</div>
            </div>
            <div
              class="top-users"
              @click="contributionPopupRef.open(top1)"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '5',
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              <div class="top-users-1">
                <div class="top-users-inner">
                  <img :src="(top1?.top || [])[0]?.avatar" />
                  <div class="tag">1</div>
                </div>
              </div>
              <div class="top-users-2">
                <div class="top-users-inner">
                  <img :src="(top1?.top || [])[1]?.avatar" />
                  <div class="tag">2</div>
                </div>
              </div>
              <div class="top-users-3">
                <div class="top-users-inner">
                  <img :src="(top1?.top || [])[2]?.avatar" />
                  <div class="tag">3</div>
                </div>
              </div>
              <div class="top-users-more"></div>
            </div>
          </div>
          <div class="top-3">
            <div class="room-cover">
              <img
                v-if="top3"
                :src="top3?.cover"
                @click="goVoiceRoom(top3?.room_id)"
                v-track:click
                trace-key="room_join_click"
                :track-params="
                  JSON.stringify({
                    room_join_click_source: '90',
                  })
                "
              />
              <div v-else class="empty-avatar">
                <svg-icon icon="vector" :size="20"></svg-icon>
              </div>
              <div class="room-top3-cover"></div>
            </div>
            <div class="nickname">{{ top3?.name || "-" }}</div>
            <div class="coin-box">
              <div class="pk-value-icon"></div>
              <gap :gap="3"></gap>
              <div class="coin-number">{{ formatCount(top3?.coin ?? 0) }}</div>
            </div>
            <div
              class="top-users"
              @click="contributionPopupRef.open(top3)"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '5',
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              <div class="top-users-1">
                <div class="top-users-inner">
                  <img :src="(top3?.top || [])[0]?.avatar" />
                  <div class="tag">1</div>
                </div>
              </div>
              <div class="top-users-2">
                <div class="top-users-inner">
                  <img :src="(top3?.top || [])[1]?.avatar" />
                  <div class="tag">2</div>
                </div>
              </div>
              <div class="top-users-3">
                <div class="top-users-inner">
                  <img :src="(top3?.top || [])[2]?.avatar" />
                  <div class="tag">3</div>
                </div>
              </div>
              <div class="top-users-more"></div>
            </div>
          </div>
        </div>
        <div class="ranking-list">
          <div
            class="rank-item"
            v-for="(item, index) in rankAfter3List"
            :key="index"
          >
            <div class="index">{{ item.rank }}</div>
            <gap :gap="8"></gap>
            <div class="room-cover">
              <img
                v-if="item"
                :src="item?.cover"
                @click="goVoiceRoom(item?.room_id)"
                v-track:click
                trace-key="room_join_click"
                :track-params="
                  JSON.stringify({
                    room_join_click_source: '90',
                  })
                "
              />
              <div v-else class="empty-avatar">
                <svg-icon icon="vector" :size="20"></svg-icon>
              </div>
              <div class="room-top4-cover"></div>
            </div>
            <gap :gap="8"></gap>
            <div class="nickname">
              <div>{{ item.room_name || "-" }}</div>
              <div class="coin-box">
                <div class="pk-value-icon"></div>
                <gap :gap="3"></gap>
                <div class="coin-number">
                  {{ formatCount(item?.coin ?? 0) }}
                </div>
              </div>
            </div>
            <gap :gap="8"></gap>
            <div
              class="top-users"
              @click="contributionPopupRef.open(item)"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '5',
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              <div class="top-users-1">
                <div class="top-users-inner">
                  <img :src="(item?.top || [])[0]?.avatar" />
                  <div class="tag">1</div>
                </div>
              </div>
              <div class="top-users-2">
                <div class="top-users-inner">
                  <img :src="(item?.top || [])[1]?.avatar" />
                  <div class="tag">2</div>
                </div>
              </div>
              <div class="top-users-3">
                <div class="top-users-inner">
                  <img :src="(item?.top || [])[2]?.avatar" />
                  <div class="tag">3</div>
                </div>
              </div>
              <div class="top-users-more"></div>
            </div>
            <gap :gap="16"></gap>
          </div>
        </div>
        <div class="bottom-box">
          <div class="bottom-box-inner">
            <div class="rank-item">
              <div class="index">
                {{
                  currentRank?.rank === -1
                    ? "-"
                    : (currentRank?.rank ?? 0) > 99
                    ? "99+"
                    : currentRank?.rank || "-"
                }}
              </div>
              <gap :gap="8"></gap>
              <div class="room-cover">
                <img
                  v-if="currentRank?.cover"
                  :src="currentRank?.cover"
                  @click="goVoiceRoom(currentRank?.room_id)"
                  v-track:click
                  trace-key="room_join_click"
                  :track-params="
                    JSON.stringify({
                      room_join_click_source: '90',
                    })
                  "
                />
                <div v-else class="empty-avatar">
                  <svg-icon icon="vector" :size="20"></svg-icon>
                </div>
                <div
                  v-if="currentRank?.rank === 1"
                  class="room-top1-cover"
                ></div>
                <div
                  v-else-if="currentRank?.rank === 2"
                  class="room-top2-cover"
                ></div>
                <div
                  v-else-if="currentRank?.rank === 3"
                  class="room-top3-cover"
                ></div>
                <div v-else class="room-top4-cover"></div>
              </div>
              <gap :gap="8"></gap>
              <div class="nickname">
                <div>{{ currentRank?.room_name || "-" }}</div>
                <div class="coin-box">
                  <div class="pk-value-icon"></div>
                  <gap :gap="3"></gap>
                  <div class="coin-number">
                    {{ formatCount(currentRank?.coin ?? 0) }}
                  </div>
                </div>
              </div>
              <div
                class="top-users"
                @click="contributionPopupRef.open(currentRank)"
                v-track:click
                trace-key="active_button_click"
                :track-params="
                  JSON.stringify({
                    active_button_click_name: '5',
                    active_name: 'Stellar Voyage-房主开播激励活动',
                  })
                "
              >
                <div class="top-users-1">
                  <div class="top-users-inner">
                    <img :src="(currentRank?.top || [])[0]?.avatar" />
                    <div class="tag">1</div>
                  </div>
                </div>
                <div class="top-users-2">
                  <div class="top-users-inner">
                    <img :src="(currentRank?.top || [])[1]?.avatar" />
                    <div class="tag">2</div>
                  </div>
                </div>
                <div class="top-users-3">
                  <div class="top-users-inner">
                    <img :src="(currentRank?.top || [])[2]?.avatar" />
                    <div class="tag">3</div>
                  </div>
                </div>
                <div class="top-users-more"></div>
              </div>
              <gap :gap="16"></gap>
            </div>
          </div>
        </div>
      </div>
    </GoldBox>
    <ContributionPopup ref="contributionPopupRef"></ContributionPopup>
  </div>
</template>
<script setup>
import { computed, onMounted, ref, getCurrentInstance } from "vue";
import GoldBox from "./GoldBox.vue";
import TitleBlock from "./TitleBlock.vue";
import ContributionPopup from "./ContributionPopup.vue";
import activityApi from "@/api/activity.js";
import { formatCount } from "@/utils/util.js";
const { proxy } = getCurrentInstance();
const contributionPopupRef = ref();
const rankList = ref([]);
const top1 = computed(() => {
  return rankList.value.find((i) => i.rank === 1);
});
const top2 = computed(() => {
  return rankList.value.find((i) => i.rank === 2);
});
const top3 = computed(() => {
  return rankList.value.find((i) => i.rank === 3);
});
const rankAfter3List = computed(() =>
  rankList.value.filter((i) => i.rank && i.rank > 3)
);
const currentRank = ref(null);
const hasMe = computed(
  () => currentRank.value && currentRank.value?.rank !== -1
);
const fetchData = async () => {
  const res = await activityApi.live_rank();
  if (res.code === 200) {
    rankList.value = (res.data?.items || []).map((item) => {
      return {
        ...item,
        name: item.room_name || "-",
        cover: item.cover,
        coin: item.coin || 0,
        rank: item.rank,
        frame: null,
      };
    });
    if (res.data?.user_rank) {
      currentRank.value = {
        ...res.data?.user_rank,
        name: res.data?.user_rank?.room_name || "-",
        cover: res.data?.user_rank?.cover,
        coin: res.data?.user_rank?.coin || 0,
        rank: res.data?.user_rank?.rank || -1,
        frame: null,
      };
    }
  }
};

const goVoiceRoom = (room_id) => {
  if (room_id) {
    proxy.$siyaApp("openSiyaUrl", {
      url: `siya://siya.com/app?method=goVoiceRoom&roomId=${room_id}`,
    });
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.box-title {
  margin-top: px2rem(60);
  display: flex;
  justify-content: center;
}
.box-desc {
  margin: 0 auto;
  margin-top: px2rem(14);
  color: #9da8ff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(12);
  font-style: normal;
  font-weight: 400;
  line-height: 140%; /* 16.8px */
  width: px2rem(271);
}
.ranking {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 5;
  margin-top: px2rem(16);
}

.coin-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  .pk-value-icon {
    width: px2rem(16);
    height: px2rem(16);
    background-image: url("@/assets/images/activity/stellar-voyage/value.png");
    background-size: 100% 100%;
  }
  .coin-number {
    color: #fff;
    text-shadow: 0 0 3.663px #6a00ff;
    font-family: "DIN Next W1G";
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 12.82px */
  }
}

.top-users {
  width: px2rem(78);
  height: px2rem(26);
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  padding-left: px2rem(3);
  .top-users-1,
  .top-users-2,
  .top-users-3 {
    position: relative;
    width: px2rem(23);
    height: px2rem(23);
    border-radius: px2rem(23);
    flex-shrink: 0;
    margin-left: px2rem(-3);
    background: linear-gradient(
      165deg,
      #b19aff 12.44%,
      #e9c0ff 25.62%,
      #fff 69.45%,
      #b19aff 87.56%
    );
    display: flex;
    justify-content: center;
    align-items: center;
    .top-users-inner {
      width: px2rem(20);
      height: px2rem(20);
      border-radius: px2rem(20);
      overflow: hidden;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: px2rem(20);
        height: px2rem(20);
        border-radius: px2rem(20);
        object-fit: contain;
        background-color: #220079;
      }
      .tag {
        width: px2rem(14);
        height: px2rem(14);
        border-radius: px2rem(14);
        flex-shrink: 0;
        position: absolute;
        bottom: px2rem(-7);
        box-shadow: 0.5px 0 0.2px 0 #fff inset;

        font-family: Gilroy;
        font-size: px2rem(5);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        text-align: center;
        line-height: px2rem(6);
        padding-top: px2rem(1);
      }
    }
  }
  .top-users-1 {
    z-index: 4;
    .top-users-inner {
      .tag {
        background: linear-gradient(154deg, #ffdf87 17.87%, #ffd061 83.44%);
        color: #f88000;
      }
    }
  }
  .top-users-2 {
    z-index: 3;
    .top-users-inner {
      .tag {
        background: linear-gradient(154deg, #b6ecff 17.87%, #f2f0ff 83.44%);
        color: #366699;
      }
    }
  }
  .top-users-3 {
    z-index: 2;
    .top-users-inner {
      .tag {
        background: linear-gradient(154deg, #e0ac83 17.87%, #f5dca4 83.44%);
        color: #bc5d2e;
      }
    }
  }
  .top-users-more {
    z-index: 1;
    position: relative;
    width: px2rem(16);
    height: px2rem(16);
    background-image: url("@/assets/images/activity/stellar-voyage/more.png");
    background-size: 100% 100%;
    margin-left: px2rem(-3);
    flex-shrink: 0;
  }
}
.top3 {
  width: 100%;
  height: px2rem(256);
  background-size: 100% 100%;
  background-position: -1px 0;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    width: px2rem(303);
    height: px2rem(191);
    top: px2rem(-8);
    left: 50%;
    transform: translateX(-50%);
    background-image: url("@/assets/images/activity/stellar-voyage/light.png"),
      url("@/assets/images/activity/stellar-voyage/light-2.png");
    background-size: 100% 100%, 100% 100%;
  }
  .top-1,
  .top-2,
  .top-3 {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    &::after {
      content: "";
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      position: absolute;
      inset: 0;
      z-index: 0;
    }
    .room-cover {
      width: px2rem(74);
      height: px2rem(71);
      margin-top: px2rem(43);
      z-index: 10;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: px2rem(28);
      img {
        width: px2rem(50);
        height: px2rem(50);
        border-radius: px2rem(6);
        object-fit: cover;
        z-index: 1;
      }
      .room-top1-cover {
        width: px2rem(74);
        height: px2rem(71);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top1-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
      }
      .room-top2-cover {
        width: px2rem(74);
        height: px2rem(69);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top2-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
      }
      .room-top3-cover {
        width: px2rem(67);
        height: px2rem(63);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top3-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
      }
    }
    .nickname {
      margin-top: px2rem(-1);
      color: #fff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(12);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      width: px2rem(90);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      z-index: 10;
      position: relative;
    }
    .top-users {
      margin-top: px2rem(15);
    }
    .coin-box {
      margin-top: px2rem(4);
      z-index: 10;
      position: relative;
    }
  }
  .top-1 {
    width: px2rem(110);
    height: px2rem(214);
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    z-index: 2;
    &::after {
      background-image: url("@/assets/images/activity/stellar-voyage/top1.png");
    }
  }
  .top-2 {
    width: px2rem(103);
    height: px2rem(205);
    left: px2rem(30);
    top: px2rem(51);
    z-index: 2;
    &::after {
      background-image: url("@/assets/images/activity/stellar-voyage/top2.png");
    }
    .room-cover {
      margin-top: px2rem(34);
      width: px2rem(74);
      height: px2rem(69);
      .empty-avatar {
        margin-top: px2rem(10);
      }
    }
    .coin-box {
      margin-top: px2rem(2);
    }
  }
  .top-3 {
    width: px2rem(103);
    height: px2rem(205);
    right: px2rem(30);
    top: px2rem(51);
    z-index: 2;
    &::after {
      background-image: url("@/assets/images/activity/stellar-voyage/top3.png");
    }
    .room-cover {
      margin-top: px2rem(36);
      width: px2rem(67);
      height: px2rem(63);
      padding-top: px2rem(10);
      .empty-avatar {
        margin-top: px2rem(2);
      }
    }
    .nickname {
      margin-top: px2rem(3);
    }
  }
}
.ranking-list {
  width: 100%;
  margin-top: px2rem(16);
  padding-bottom: px2rem(98);
  z-index: 3;
  .rank-item {
    display: flex;
    height: px2rem(60);
    width: px2rem(331);
    margin: 0 auto;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: px2rem(8);
    border: 1px solid rgba(255, 255, 255, 0.28);
    background: linear-gradient(
      90deg,
      rgba(15, 12, 78, 0.29) 0%,
      rgba(2, 0, 43, 0.29) 100%
    );
    margin-bottom: px2rem(6);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
      flex-shrink: 0;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .room-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      width: px2rem(38);
      height: px2rem(38);
      border-radius: px2rem(6);
      font-size: px2rem(20);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
        position: relative;
        z-index: 1;
      }
      .room-top4-cover {
        width: px2rem(38);
        height: px2rem(38);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top4-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
      }
    }

    .nickname {
      color: var(---White, #fff);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 700;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }
    .coin-box {
      justify-content: flex-start;
      margin-top: px2rem(2);
      .coin-number {
        color: #fff;
      }
    }
  }
}
.pb103 {
  padding-bottom: px2rem(103);
}
.rtl-html {
  .rank-item {
    .index {
      text-align: left;
    }
  }
}

.bottom-box {
  width: px2rem(391);
  height: fit-content;
  flex-shrink: 0;
  position: fixed;
  left: 0;
  bottom: px2rem(-1);
  z-index: 99;
  background: linear-gradient(180deg, #d1b4ff 0%, #3c12a6 80%, #3c12a6 0%);
  padding: px2rem(2);
  border-radius: px2rem(16) px2rem(16) 0 0;
  overflow: hidden;
  .bottom-box-inner {
    overflow: hidden;
    background: linear-gradient(
      223deg,
      #260054 -19.77%,
      #220079 52.88%,
      #3c12a6 112.1%
    );
    width: 100%;
    height: px2rem(91);
    border-radius: px2rem(14) px2rem(14) 0 0;
  }
  .rank-item {
    display: flex;
    align-items: center;
    margin: 0 auto;
    margin-top: px2rem(11);
    width: px2rem(331);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .room-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      width: px2rem(38);
      height: px2rem(38);
      border-radius: px2rem(6);
      font-size: px2rem(20);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
      .room-top1-cover {
        width: px2rem(38);
        height: px2rem(38);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top1-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
        transform: scale(1.25);
      }
      .room-top2-cover {
        width: px2rem(38);
        height: px2rem(38);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top2-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
        transform: scale(1.25);
      }
      .room-top3-cover {
        width: px2rem(38);
        height: px2rem(38);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top3-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
        transform: scale(1.3);
        margin-top: px2rem(-3);
      }
      .room-top4-cover {
        width: px2rem(38);
        height: px2rem(38);
        position: absolute;
        inset: 0;
        z-index: 2;
        background-image: url("@/assets/images/activity/stellar-voyage/top4-avatar.png");
        background-size: 100% 100%;
        pointer-events: none;
      }
    }
    .nickname {
      color: var(---White, #fff);

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }

    .coin-box {
      justify-content: flex-start !important;
      .coin-number {
        color: #fff;
      }
    }
  }
  .bottom-desc {
    display: flex;
    height: px2rem(28);
    flex-direction: column;
    justify-content: center;

    position: absolute;
    right: px2rem(18);
    top: px2rem(43);
    text-align: right;
    font-family: Gilroy;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 10px */
    & > div:first-child {
      color: #ff9791;
    }
    & > div:last-child {
      color: #cc5c55;
      margin-top: px2rem(8);
    }
  }
}

.empty-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>

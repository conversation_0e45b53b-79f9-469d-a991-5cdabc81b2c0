<template>
  <van-overlay
    class-name="prize-dialog"
    :show="showDialog"
    @touchmove.prevent
    z-index="10001"
  >
    <div v-if="showDialog" class="base-modal-wrapper" @click.stop>
      <div class="box">
        <div class="ap"></div>
        <div class="title">
          <TitleBlock
            :title="$t('stellar_voyage.congratulations')"
            :showBg="false"
          ></TitleBlock>
        </div>
        <div class="rewards">
          <div class="reward" v-for="prize in prizes">
            <div class="cover">
              <PrizeBox :rewards="prize"></PrizeBox>
            </div>
            <div class="name">{{ prize.reward_name }}</div>
          </div>
        </div>
        <div class="desc">
          {{ $t("stellar_voyage.task_reward_sent") }}
        </div>
        <div class="action">
          <LButton
            :width="168"
            :height="38"
            :font-size="18"
            @click="showDialog = false"
          >
            {{ $t("stellar_voyage.ok") }}
          </LButton>
        </div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";
import TitleBlock from "./TitleBlock.vue";
import LButton from "./LButton.vue";
import PrizeBox from "./PrizeBox.vue";

const showDialog = ref(false);
const prizes = ref([]);

const handleOpen = (data) => {
  prizes.value = data || [];
  showDialog.value = true;
};

defineExpose({
  open: handleOpen,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.88);
}
@keyframes show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .box {
    width: px2rem(331);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    position: relative;
    border-radius: px2rem(13);
    border: px2rem(1) solid #8b8dff;
    background: linear-gradient(0deg, #5862d0 -25.4%, #140567 37.01%);
    box-shadow: 0 0 px2rem(23) 0 #1b16a9 inset;
    padding-bottom: px2rem(25);
    .ap {
      position: absolute;
      width: px2rem(184);
      height: px2rem(109);
      top: px2rem(-50);
      left: 50%;
      transform: translateX(-50%);
      background-image: url("@/assets/images/activity/stellar-voyage/ap.png");
      background-size: 100% 100%;
    }
    .title {
      margin-top: px2rem(50);
    }
    .desc {
      margin-top: px2rem(6);
      color: #7686ff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(12);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      width: px2rem(268);
    }
    .rewards {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: px2rem(17) 0;
      .reward {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: px2rem(87);
        .cover {
          width: px2rem(62);
          height: px2rem(62);
          border-radius: px2rem(8);
          box-shadow: 0 0 10px 0 rgba(254, 192, 255, 0.5);
        }
        .name {
          margin-top: px2rem(12);
          width: px2rem(87);
          color: #7686ff;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 14px */
        }
      }
    }

    .action {
      margin-top: px2rem(20);
    }
  }
}
</style>

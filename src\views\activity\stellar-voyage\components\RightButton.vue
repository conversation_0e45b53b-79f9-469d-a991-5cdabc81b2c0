<template>
  <div
    class="right-button"
    v-track:click
    trace-key="active_button_click"
    :track-params="
      JSON.stringify({
        active_button_click_name: '1',
        active_name: 'Stellar Voyage-房主开播激励活动',
      })
    "
  >
    <div class="right-button-text" @click="toRulePage()">
      {{ $t(`singer.rules_title`) }}
    </div>
  </div>
</template>
<script setup>
import { useRouter } from "vue-router";
const router = useRouter();

const toRulePage = () => {
  router.push({
    name: "StellarVoyageRule",
  });
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.right-button {
  height: px2rem(30);
  width: fit-content;
  position: fixed;
  right: px2rem(-2);
  top: px2rem(217);
  z-index: 999;
  border-radius: px2rem(30) 0 0 px2rem(30);
  border: 1px solid #fff;
  background: linear-gradient(
    180deg,
    #d5f4ff 0%,
    #ea98ff 13.94%,
    #791ff6 36.02%,
    #3900f5 83.17%
  );
  box-shadow: 0px -2.454px 2.454px 0px rgba(44, 2, 104, 0.64) inset,
    0px 1.84px 1.84px 0px rgba(255, 255, 255, 0.66) inset;
  display: flex;
  align-items: center;
  padding: 0 px2rem(4) 0 px2rem(12);
  .right-button-text {
    color: #fff;
    text-align: center;
    text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 700;
    line-height: 124%; /* 14.88px */
  }
}

.rtl-html {
  .right-button {
    right: unset;
    left: px2rem(-2);
    border-radius: 0 px2rem(30) px2rem(30) 0;
    .right-button-text {
      padding: 0 px2rem(12) 0 px2rem(4);
    }
  }
}
</style>

<template>
  <div class="task-process">
    <div class="process">
      <div class="process-inner" :style="{ width: `${processWidth}%` }"></div>
    </div>
    <div class="steps">
      <div v-for="(task, index) in tasks" :key="index" class="step">
        <div class="p-cur" v-if="index === current"></div>
        <div class="p-prev" v-else-if="task.status === 2"></div>
        <div class="p-next" v-else></div>
      </div>
    </div>
    <div class="rewards">
      <div v-for="(task, index) in tasks" :key="index" class="reward">
        <div class="cover" :class="{ shadow: task.status > 0 }">
          <PrizeBox :rewards="task.rewards" :show-tag="true"></PrizeBox>
        </div>
        <div class="name">{{ $t("stellar_voyage.day", [index + 1]) }}</div>
      </div>
    </div>
    <div class="btn-go">
      <LButton
        v-if="todayStatus === 0"
        :width="146"
        :height="34"
        @click="emit('go')"
        v-track:click
        trace-key="active_button_click"
        :track-params="
          JSON.stringify({
            active_button_click_name: '3',
            active_button_click_status: '1',
            active_name: 'Stellar Voyage-房主开播激励活动',
          })
        "
      >
        {{ $t("stellar_voyage.go") }}
      </LButton>
      <LButton
        v-if="todayStatus === 1"
        :width="146"
        :height="34"
        type="active"
        @click="emit('receive', receiveKey)"
        v-track:click
        trace-key="active_button_click"
        :track-params="
          JSON.stringify({
            active_button_click_name: '3',
            active_button_click_status: '2',
            active_name: 'Stellar Voyage-房主开播激励活动',
          })
        "
      >
        {{ $t("stellar_voyage.claim") }}
      </LButton>
      <LButton
        v-if="todayStatus === 2"
        :width="146"
        :height="34"
        type="disabled"
        v-track:click
        trace-key="active_button_click"
        :track-params="
          JSON.stringify({
            active_button_click_name: '3',
            active_button_click_status: '3',
            active_name: 'Stellar Voyage-房主开播激励活动',
          })
        "
      >
        {{ $t("stellar_voyage.claimed") }}
      </LButton>
    </div>
  </div>
</template>
<script setup>
import { computed, ref } from "vue";
import PrizeBox from "./PrizeBox.vue";
import LButton from "./LButton.vue";

const emit = defineEmits(["go", "receive"]);
const props = defineProps({
  todayStatus: {
    type: Number,
    default: 0,
  },
  tasks: {
    type: Array,
    default: () => [],
  },
});
const status = computed(() => {
  return props.tasks.map((i) => i.status);
});
const rewards = computed(() => {
  return props.tasks.map((i) => i.rewards);
});
const current = computed(() => {
  const index = status.value.findIndex((i) => i === 0);
  return index - 1;
});
const processWidth = computed(() => {
  const index = status.value.findIndex((i) => i === 0);
  return index === -1 ? 0 : ((index - 1) * 100) / (rewards.value.length - 1);
});
const receiveKey = computed(() => {
  return `live_${current.value + 1}_day`;
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.task-process {
  margin-top: px2rem(14);
  width: 100%;
  position: relative;
  .process {
    position: absolute;
    left: 50%;
    top: px2rem(8);
    transform: translateX(-50%);
    width: px2rem(294);
    height: px2rem(6);
    border-radius: px2rem(4);
    background: #341f91;
    .process-inner {
      height: 100%;
      flex-shrink: 0;
      border-radius: px2rem(4);
      background: linear-gradient(90deg, #868eff 56.63%, #e8e6ff 110.35%);
    }
  }
  .steps {
    width: 100%;
    display: flex;
    justify-content: center;
    height: px2rem(24);
    position: relative;
    z-index: 1;
    .step {
      display: flex;
      justify-content: center;
      width: px2rem(48);
      .p-next,
      .p-cur,
      .p-prev {
        width: px2rem(20);
        height: px2rem(20);
        background-size: 100% 100%;
        flex-shrink: 0;
      }
      .p-prev {
        background-image: url("@/assets/images/activity/stellar-voyage/p-prev.png");
      }
      .p-next {
        background-image: url("@/assets/images/activity/stellar-voyage/p-next.png");
      }
      .p-cur {
        width: px2rem(24);
        height: px2rem(24);
        background-image: url("@/assets/images/activity/stellar-voyage/p-cur.png");
      }
    }
  }
  .rewards {
    width: 100%;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
    margin-top: px2rem(12);
    .reward {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: px2rem(48);

      .cover {
        width: px2rem(32);
        height: px2rem(32);
        border-radius: px2rem(6);
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .shadow {
        box-shadow: 0 0 10px 0 rgba(254, 192, 255, 0.5);
      }
      .name {
        color: #9da8ff;
        text-align: center;
        font-family: Gilroy-Medium;
        font-size: px2rem(11);
        font-style: normal;
        font-weight: 400;
        line-height: 130%; /* 14.3px */
        margin-top: px2rem(7);
      }
    }
  }
  .btn-go {
    display: flex;
    justify-content: center;
    margin-top: px2rem(14);
  }
}
</style>

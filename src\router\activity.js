const routes = [
  /* ===== 主播周工资 ===== */
  {
    path: "/anchor-week",
    name: "AnchorWeek",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "AnchorWeek"*/ "../views/activity/anchor-week/Index.vue"
      ),
  },
  /* ===== 四月15日阿拉伯活动 ===== */
  {
    path: "/activity/april-15th",
    name: "April15th",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "April15th"*/ "../views/activity/april-15th/Index.vue"
      ),
  },
  /* ===== 五月30号转盘活动 ===== */
  {
    path: "/activity/wheel-lottery/:theme?",
    name: "WheelLottery",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "WheelLottery"*/ "../views/activity/wheel-lottery/Index.vue"
      ),
  },
  {
    path: "/activity/wheel-lottery-rule/:theme?",
    name: "WheelLotteryRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "WheelLotteryRule"*/ "../views/activity/wheel-lottery/Rule.vue"
      ),
  },
  /* ===== 五月23号星推官活动 ===== */
  {
    path: "/activity/promoter-star",
    name: "PromoterStar",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PromoterStar"*/ "../views/activity/promoter-star/Index.vue"
      ),
  },
  {
    path: "/activity/promoter-submit",
    name: "PromoterSubmit",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PromoterSubmit"*/ "../views/activity/promoter-star/Submit.vue"
      ),
  },
  {
    path: "/activity/promoter-ranking",
    name: "PromoterRanking",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PromoterRanking"*/ "../views/activity/promoter-star/Ranking.vue"
      ),
  },
  /* ===== 大R周累充活动- Siya大亨 ===== */
  {
    path: "/activity/recharge-ranking",
    name: "RechargeRanking",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "RechargeRanking"*/ "../views/activity/recharge-ranking/Index.vue"
      ),
  },
  {
    path: "/activity/recharge-ranking-rule",
    name: "RechargeRankingRule",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "RechargeRankingRule"*/ "../views/activity/recharge-ranking/Rule.vue"
      ),
  },
  /* ===== PK Limited Time Rewards ===== */
  {
    path: "/activity/poster",
    name: "Poster",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(/*webChunkName: "Poster"*/ "../views/activity/poster/Index.vue"),
  },
  /* ===== Siya歌手大赛 ===== */
  {
    path: "/activity/singer",
    name: "SingerIndex",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "SingerIndex"*/ "../views/activity/singer/Index.vue"
      ),
  },
  {
    path: "/activity/singer-rule",
    name: "SingerRule",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "SingerRule"*/ "../views/activity/singer/Rule.vue"
      ),
  },
  /* ===== 埃及国庆活动 ===== */
  {
    path: "/activity/egypt-national-day",
    name: "EgyptNationalDay",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "EgyptNationalDay"*/ "../views/activity/egypt-national-day/Index.vue"
      ),
  },
  {
    path: "/activity/egypt-national-day-rule",
    name: "EgyptNationalDayRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "EgyptNationalDayRule"*/ "../views/activity/egypt-national-day/Rule.vue"
      ),
  },
  /* ===== 7.24PK海报 ===== */
  {
    path: "/activity/poster/PK724",
    name: "PK724",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "PK724"*/ "../views/activity/poster/PK724.vue"),
  },
  /* ===== 7.29PK王活动 ===== */
  {
    path: "/activity/pk-king",
    name: "PKKing",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "PKKing"*/ "../views/activity/pk-king/Index.vue"),
  },
  {
    path: "/activity/pk-king-rule",
    name: "PKKingRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PKKingRule"*/ "../views/activity/pk-king/Rule.vue"
      ),
  },
  // 房间派对活动的奖励弹窗
  {
    path: "/activity/partySponsor",
    name: "ActivityPartySponsor",
    meta: {
      fullScreen: false,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "ActivityPartySponsor"*/ "../views/activity/partySponsor/Index.vue"
      ),
  },
  /* ===== 7.24PK海报 ===== */
  {
    path: "/activity/poster/party",
    name: "Party",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "Party"*/ "../views/activity/poster/Party.vue"),
  },
  /* ===== 8.11开播奖励活动 ===== */
  {
    path: "/activity/stellar-voyage",
    name: "StellarVoyage",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PKKing"*/ "../views/activity/stellar-voyage/Index.vue"
      ),
  },
  {
    path: "/activity/stellar-voyage-rule",
    name: "StellarVoyageRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PKKingRule"*/ "../views/activity/stellar-voyage/Rule.vue"
      ),
  },
];

export default routes;

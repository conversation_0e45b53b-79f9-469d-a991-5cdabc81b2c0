<template>
  <div class="content">
    <header-bar :padding="false" closeType="close" fontColor="#fff" />
    <div class="title">{{ $t('education.education_1') }}</div>
    <div class="son-title">
      {{ $t('education.education_80') }}
    </div>
    <div class="tips">
      {{ $t('education.education_81') }}{{ $t('education.education_82') }}
    </div>
    <div class="love-content">
      <div class="love-title">{{ $t('education.education_83') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_84').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_84').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_85').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_85').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-content">
        <div class="wrong-content-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <img :src="getImageUrl('1-1.png')" alt="">
        </div>
        <div class="wrong-content-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <img :src="getImageUrl('1-2.png')" alt=""></img>
        </div>
      </div>
    </div>
    <div class="love-content">
      <div class="love-title">{{ $t('education.education_88') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_89').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_89').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_90').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_90').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-column">
        <div class="wrong-column-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <div class="wrong-item-img">
            <div class="img-wrap">
              <img src="@/assets/images/education/miss-love/2-1.png" alt="" />
              <p class="wrong-item-img-text">{{ $t('education.education_92') }}</p>
            </div>
            <div class="img-wrap">
              <img src="@/assets/images/education/miss-love/2-2.png" alt="" />
              <p class="wrong-item-img-text">{{ $t('education.education_93') }}</p>
            </div>
          </div>
        </div>
          <div class="wrong-column-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <div class="wrong-item-img">
            <div class="img-wrap">
              <img src="@/assets/images/education/miss-love/2-3.png" alt="" />
            </div>
            <div class="img-wrap">
              <img src="@/assets/images/education/miss-love/2-4.png" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="love-content">
      <div class="love-title">{{ $t('education.education_95') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_96').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_96').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_97').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_97').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-wrap">
        <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
        <div class="wrong-item">
          <img src="@/assets/images/education/miss-love/3-1.png" alt=""></img>
          <div class="wrong-item-img-text">{{ $t('education.education_99') }}</div>
        </div>
        <div class="wrong-item">
          <img src="@/assets/images/education/miss-love/3-2.png" alt=""></img>
          <div class="wrong-item-img-text">{{ $t('education.education_100') }}</div>
        </div>
        <div class="wrong-item">
          <img src="@/assets/images/education/miss-love/3-3.png" alt=""></img>
          <div class="wrong-item-img-text">{{ $t('education.education_101') }}</div>
        </div>
        <div class="wrong-item">
          <img src="@/assets/images/education/miss-love/3-4.png" alt=""></img>
          <div class="wrong-item-img-text">{{ $t('education.education_102') }}</div>
        </div>
        <div class="right-item-title mt20">{{ $t('education.education_136') }}</div>
        <div class="wrong-item">
          <img class="right-img" src="@/assets/images/education/miss-love/3-5.png" alt=""></img>
        </div>
      </div>
    </div>
    <div class="love-content">
      <div class="love-title">{{ $t('education.education_104') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_105').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_105').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_106').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_106').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-content">
        <div class="wrong-content-item" v-for="item in 2" :key="item">
          <div v-if="item==1" class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <div v-else class="right-item-title">{{ $t('education.education_136') }}</div>
          <img v-if="item==1" src="@/assets/images/education/miss-love/4-1.png" alt="">
          <img v-else src="@/assets/images/education/miss-love/4-2.png" alt=""></img>
        </div>
      </div>
    </div>
    <div class="son-title">
      {{ $t('education.education_107') }}
    </div>
    <div class="love-content mt20">
      <div class="love-title">{{ $t('education.education_108') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_109').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_109').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_110').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_110').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-column">
        <div class="wrong-column-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <img class="chat-img" :src="getImageUrl('5-1.png')" alt="" />
        </div>
        <div class="wrong-column-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <img class="chat-img" :src="getImageUrl('5-2.png')" alt="" />
        </div>
      </div>
    </div>
    <div class="love-content mt20">
      <div class="love-title">{{ $t('education.education_117') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_118').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_118').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_119').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_119').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-column">
        <div class="wrong-column-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <img class="chat-img" :src="getImageUrl('6-1.png')" alt="" />
        </div>
        <div class="wrong-column-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <img class="chat-img" :src="getImageUrl('6-2.png')" alt="" />
        </div>
      </div>
    </div>
    <div class="love-content mt20">
      <div class="love-title">{{ $t('education.education_128') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_129').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_129').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_130').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_130').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-column">
        <div class="wrong-column-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <img class="chat-img" :src="getImageUrl('7-1.png')" alt="" />
        </div>
        <div class="wrong-column-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <img class="chat-img" :src="getImageUrl('7-2.png')" alt="" />
        </div>
      </div>
    </div>
    <div class="love-content mt20">
      <div class="love-title">{{ $t('education.education_143') }}</div>
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_144').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_144').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_145').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_145').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-column">
        <div class="wrong-column-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <img class="chat-img" :src="getImageUrl('8-1.png')" alt="" />
        </div>
        <div class="wrong-column-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <img class="chat-img" :src="getImageUrl('8-2.png')" alt="" />
        </div>
      </div>
    </div>
    <div class="son-title">
      {{ $t('education.education_156') }}
    </div>
    <div class="love-content mt20">
      <div class="love-text-content">
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_157').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_157').split(':')[1] }}:</span>
        </div>
        <div class="love-text">
          <span class="love-text-title">{{ $t('education.education_158').split(':')[0] }}:</span>&nbsp;
          <span>{{ $t('education.education_158').split(':')[1] }}:</span>
        </div>
      </div>
      <div class="wrong-content">
        <div class="wrong-content-item">
          <div class="wrong-item-title">{{ $t('education.education_131') }}</div>
          <img src="@/assets/images/education/miss-love/9-1.png" alt="">
        </div>
        <div class="wrong-content-item">
          <div class="right-item-title">{{ $t('education.education_136') }}</div>
          <img src="@/assets/images/education/miss-love/9-2.png" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance,ref,onMounted } from 'vue'
import { i18n } from '@/i18n/index.js'

const t = i18n.global.t
const { proxy } = getCurrentInstance()

const lang = proxy.$languageFile

function getImageUrl(name) {
  let langstr = lang
  if(lang === 'zh-CN' || lang === 'zh-TW') {
    langstr = 'en'
  }
  return new URL(
    `../../../../assets/images/education/miss-love/${langstr}/${name}`,
    import.meta.url
  ).href;
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.content {
  background: 
    url("@/assets/images/education/miss-love/bg.png") no-repeat top center / 100vw auto,
    linear-gradient(to bottom, transparent, #6EE0E8);
  background-color: #6EE0E8;
  min-height: 100vh;
  padding: px2rem(80) px2rem(20) px2rem(20) px2rem(20);
  .title {
    color: #062F3E;
    text-align: center;
    text-shadow: 0 px2rem(4) px2rem(4.2) rgba(97, 20, 109, 0.40);
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #3DF2FF;
    font-family: Gilroy;
    font-size: px2rem(40);
    font-style: italic;
    font-weight: 900;
    line-height: px2rem(40);
    letter-spacing: -0.32px;
    margin-bottom: px2rem(258);
  }
  .son-title {
    padding: px2rem(16) px2rem(26);
    border-radius: px2rem(36);
    border: 1px solid #193F4E;
    background: linear-gradient(90deg, #81F6FF 0%, #69D8FF 55.89%, #60F4FF 100%);
    box-shadow: 0 px2rem(4) px2rem(8.8) 0 rgba(0, 0, 0, 0.14);
    color: #193F4E;
    text-align: center;
    text-shadow: 0 px2rem(2) px2rem(4) rgba(114, 0, 89, 0.20);
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #FFF;
    font-size: px2rem(20);
    font-weight: 900;
    line-height: px2rem(21); 
    letter-spacing: -0.32px;
  }
  .tips {
    border-radius: px2rem(8);
    background: rgba(210, 252, 255, 0.34);
    color: #42AEB5;
    text-align: center;
    font-size: px2rem(12);
    padding: px2rem(13) px2rem(16);
    margin-top: px2rem(12);
    margin-bottom: px2rem(16);
  }
  .love-content {
    border-radius: px2rem(12);
    background: linear-gradient(149deg, #BCFBFF 6.24%, #FFF 40.52%, #FFF 60.91%, #D2FCFF 91.96%);
    box-shadow: 0 px2rem(5) px2rem(6) 0 rgba(128, 0, 89, 0.24);
    margin-bottom: px2rem(30);
    .love-title {
      height: px2rem(52);
      border-radius: px2rem(12) px2rem(12) 0 0;
      background: linear-gradient(90deg, #81F6FF 0%, #69D8FF 55.89%, #60F4FF 100%);
      color: #193F4E;
      text-shadow: 0 px2rem(2) px2rem(4) rgba(114, 0, 89, 0.20);
      -webkit-text-stroke-width: 1px;
      -webkit-text-stroke-color: #FFF;
      font-size: px2rem(18);
      font-weight: 900;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .love-text-content {
      padding: px2rem(16) px2rem(25);
      .love-text {
        display: flex;
        font-size: px2rem(14);
        justify-content: flex-start;
        color: #26797C;
        .love-text-title{ 
          font-weight: 700;
          white-space: nowrap;
        }
      }
    }
    .wrong-item-title,.right-item-title {
      border-radius: px2rem(12);
      border: 0.8px solid #193F4E;
      background: #FF3B3B;
      color: #193F4E;
      text-align: center;
      font-size: px2rem(12);
      font-weight: 700;
      padding: px2rem(4) px2rem(10);
      width: fit-content;
    }
    .right-item-title {
      background: #67FF88;
    }
    .wrong-content {
      display: flex;
      justify-content: space-between;
      padding: 0 px2rem(36);
      .wrong-content-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: px2rem(30);
        &>img {
          width: px2rem(120);
          height: px2rem(190);
          margin-top: px2rem(15);
        }
      }
    }
    .wrong-wrap {
      padding: 0 px2rem(36);
      .right-img {
        height: px2rem(126);
        margin-bottom: px2rem(30);
      }
      .wrong-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        &>img {
          height: px2rem(126);
          margin-top: px2rem(15);
        }
      }
    }
    .wrong-column {
      padding: 0 px2rem(36);
      margin-top: px2rem(15);
      .wrong-item-img {
        display: flex;
        justify-content: space-between;
        .img-wrap {
          display: flex;
          align-items: center;
          flex-direction: column;
          margin-bottom: px2rem(26);
          &> img {
            width: px2rem(124);
            margin-top: px2rem(16);
          }
        }
      }
      .chat-img {
        width: 100%;
        margin-top: px2rem(12);
        margin-bottom: px2rem(20);
      }
    }
    .wrong-item-img-text {
      color: #25797C;
      border-radius: px2rem(12);
      border: 1px solid #25797C;
      font-size: px2rem(12);
      width: fit-content;
      padding: px2rem(2) px2rem(10);
    }
  }
}
.mt20 {
  margin-top: px2rem(20);
}
</style>

<template>
  <div class="tasks-block">
    <GoldBox>
      <div class="box-title">
        <TitleBlock
          :title="$t('stellar_voyage.stellar_energy_gifts')"
        ></TitleBlock>
      </div>
      <div class="box-desc">
        {{ $t("stellar_voyage.gift_count_rule") }}
      </div>
      <div class="box-value">
        <gender-coin :gender="11" :size="18"></gender-coin>
        <gap :gap="2"></gap>
        <span>x</span>
        <span>1</span>
        <span>=</span>
        <gap :gap="2"></gap>
        <div class="value-icon"></div>
        <gap :gap="2"></gap>
        <span>x</span>
        <span>1</span>
      </div>
      <div class="rewards">
        <div class="reward" v-for="prize in activityInfo.gifts || []">
          <div class="cover">
            <PrizeBox :rewards="prize"></PrizeBox>
          </div>
          <div class="coin-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(prize?.coin ?? 0) }}</div>
          </div>
        </div>
      </div>
    </GoldBox>
    <GoldBox>
      <div class="box-title">
        <TitleBlock :title="$t('stellar_voyage.daily_voyage')"></TitleBlock>
      </div>
      <div class="box-desc">
        {{ $t("stellar_voyage.daily_task_rule") }}
      </div>
      <div class="box-count-down">
        <CountDown
          :left-time="
            (activityInfo?.countdown || 0) - activityInfo?.requestTime
          "
        ></CountDown>
      </div>
      <div class="sub-title" @click="handleRuleDialog()">
        <TitleBlock
          :title="$t('stellar_voyage.broadcast_30_minutes')"
          :showBg="false"
          :fontSize="16"
          :lineHeight="19"
          :minWidth="0"
        ></TitleBlock>
        <gap :gap="2"></gap>
        <div class="qa"></div>
      </div>
      <TaskProcess
        :tasks="day2HoursTasks"
        :todayStatus="activityInfo?.weekly_tasks?.total_live_status"
        @go="handleGo(goCreateRoomUrl)"
        @receive="handleReceive"
      ></TaskProcess>
      <div class="task-list">
        <div class="task" v-for="(item, index) in dayTasks" :key="index">
          <div class="cover">
            <PrizeBox :rewards="item.rewards"></PrizeBox>
          </div>
          <gap :gap="8"></gap>
          <div class="detail">
            <div class="detail-desc">{{ item.desc }}</div>
            <div class="process">
              <Process :current="item.current" :max="item.max"></Process>
            </div>
            <div class="process-desc">
              ({{ formatCount(item.current) }}/{{ formatCount(item.max) }})
            </div>
          </div>
          <gap :gap="8"></gap>
          <div class="action">
            <LButton
              v-if="item.status === 0"
              @click="handleGo(item.go)"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '3',
                  active_button_click_status: '1',
                  active_button_task: item.desc,
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              {{ $t("stellar_voyage.go") }}
            </LButton>
            <LButton
              v-else-if="item.status === 1"
              @click="handleReceive(item.task_name)"
              type="active"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '3',
                  active_button_click_status: '2',
                  active_button_task: item.desc,
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              {{ $t("stellar_voyage.claim") }}
            </LButton>
            <LButton
              v-else-if="item.status === 2"
              type="disabled"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '3',
                  active_button_click_status: '3',
                  active_button_task: item.desc,
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              {{ $t("stellar_voyage.claimed") }}
            </LButton>
          </div>
        </div>
      </div>
    </GoldBox>
    <GoldBox class="box2">
      <div class="box-title">
        <TitleBlock
          :title="$t('stellar_voyage.starfield_mileage')"
        ></TitleBlock>
      </div>
      <div class="box-desc">
        {{ $t("stellar_voyage.mileage_reward_rule") }}
      </div>
      <div class="task-list">
        <div class="task" v-for="(item, index) in weekTasks" :key="index">
          <div class="cover">
            <PrizeBox :rewards="item.rewards"></PrizeBox>
          </div>
          <gap :gap="8"></gap>
          <div class="detail">
            <div class="detail-desc">{{ item.desc }}</div>
            <div class="process">
              <Process :current="item.current" :max="item.max"></Process>
            </div>
            <div class="process-desc">
              ({{ formatCount(item.current) }}/{{ formatCount(item.max) }})
            </div>
          </div>
          <gap :gap="8"></gap>
          <div class="action">
            <LButton
              v-if="item.status === 0"
              @click="handleGo(item.go)"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '3',
                  active_button_click_status: '1',
                  active_button_task: item.desc,
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              {{ $t("stellar_voyage.go") }}
            </LButton>
            <LButton
              v-else-if="item.status === 1"
              @click="handleReceive(item.task_name)"
              type="active"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '3',
                  active_button_click_status: '2',
                  active_button_task: item.desc,
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              {{ $t("stellar_voyage.claim") }}
            </LButton>
            <LButton
              v-else-if="item.status === 2"
              type="disabled"
              v-track:click
              trace-key="active_button_click"
              :track-params="
                JSON.stringify({
                  active_button_click_name: '3',
                  active_button_click_status: '3',
                  active_button_task: item.desc,
                  active_name: 'Stellar Voyage-房主开播激励活动',
                })
              "
            >
              {{ $t("stellar_voyage.claimed") }}
            </LButton>
          </div>
        </div>
      </div>
    </GoldBox>
    <RewordDialog ref="rewordDialogRef"></RewordDialog>
    <RuleDialog ref="ruleDialogRef"></RuleDialog>
  </div>
</template>
<script setup>
import GoldBox from "./GoldBox.vue";
import CountDown from "./CountDown.vue";
import PrizeBox from "./PrizeBox.vue";
import Process from "./Process.vue";
import { formatCount } from "@/utils/util.js";
import { computed, getCurrentInstance, ref } from "vue";
import RewordDialog from "./RewordDialog.vue";
import TitleBlock from "./TitleBlock.vue";
import TaskProcess from "./TaskProcess.vue";
import LButton from "./LButton.vue";
import RuleDialog from "./RuleDialog.vue";
import { t } from "@/i18n/index.js";
import activityApi from "@/api/activity.js";

const props = defineProps({
  activityInfo: {
    type: Object,
    default: () => ({}),
  },
  rewordInfo: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits("refresh");
const { proxy } = getCurrentInstance();
const rewordDialogRef = ref();
const goRoomTabUrl = `siya://siya.com/app?method=goRoomTab`;
const goCreateRoomUrl = `siya://siya.com/app?method=goCreateRoom`;
// task_name枚举：
// live_5_minute：房间开播5分钟
// join_5_minute：进房停留5分钟（房主不可见）
// receive_1_gift：收到1个活动礼物
// send_1_gift：送出1个活动礼物
// live_1_day：开播1天(开播时长>=30分钟)
// live_2_day：连续开播2天
// live_3_day：连续开播3天
// live_4_day：连续开播4天
// live_5_day：连续开播5天
// live_6_day：连续开播6天
// live_7_day：连续开播7天
// room_receive_50k：房间收礼钻石数>=50,000
// room_receive_80k：房间收礼钻石数>=80,000
// room_receive_150k：房间收礼钻石数 >=150,000
// room_receive_250k：房间收礼钻石数 >=250,000
// room_receive_350k：房间收礼钻石数 >=350,000
// room_receive_500k：房间收礼钻石数 >=500,000

const day2HoursTasks = computed(() => {
  return [
    {
      rewards: props.rewordInfo?.live_1_day,
      status: props.activityInfo?.weekly_tasks?.live_1_day ?? 0,
      task_name: "live_1_day",
    },
    {
      rewards: props.rewordInfo?.live_2_day,
      status: props.activityInfo?.weekly_tasks?.live_2_day ?? 0,
      task_name: "live_2_day",
    },
    {
      rewards: props.rewordInfo?.live_3_day,
      status: props.activityInfo?.weekly_tasks?.live_3_day ?? 0,
      task_name: "live_3_day",
    },
    {
      rewards: props.rewordInfo?.live_4_day,
      status: props.activityInfo?.weekly_tasks?.live_4_day ?? 0,
      task_name: "live_4_day",
    },
    {
      rewards: props.rewordInfo?.live_5_day,
      status: props.activityInfo?.weekly_tasks?.live_5_day ?? 0,
      task_name: "live_5_day",
    },
    {
      rewards: props.rewordInfo?.live_6_day,
      status: props.activityInfo?.weekly_tasks?.live_6_day ?? 0,
      task_name: "live_6_day",
    },
    {
      rewards: props.rewordInfo?.live_7_day,
      status: props.activityInfo?.weekly_tasks?.live_7_day ?? 0,
      task_name: "live_7_day",
    },
  ];
});

const userTask = computed(() => {
  return {
    rewards: props.rewordInfo?.join_5_minute,
    desc: proxy.$t("stellar_voyage.stay_in_room_5_minutes"),
    current: props.activityInfo?.daily_tasks?.join_5_minute === 0 ? 0 : 1,
    max: 1,
    status: props.activityInfo?.daily_tasks?.join_5_minute ?? 0,
    go: goRoomTabUrl,
    task_name: "join_5_minute",
  };
});

const dayTasks = computed(() => {
  return [
    {
      rewards: props.rewordInfo?.live_5_minute,
      desc: proxy.$t("stellar_voyage.broadcast_5_minutes"),
      current: props.activityInfo?.daily_tasks?.live_5_minute === 0 ? 0 : 1,
      max: 1,
      status: props.activityInfo?.daily_tasks?.live_5_minute ?? 0,
      go: goCreateRoomUrl,
      task_name: "live_5_minute",
    },
    props.activityInfo?.is_owner ? undefined : userTask.value,
    {
      rewards: props.rewordInfo?.receive_1_gift,
      desc: proxy.$t("stellar_voyage.receive_activity_gift"),
      current: props.activityInfo?.daily_tasks?.receive_1_gift === 0 ? 0 : 1,
      max: 1,
      status: props.activityInfo?.daily_tasks?.receive_1_gift ?? 0,
      go: goRoomTabUrl,
      task_name: "receive_1_gift",
    },
    {
      rewards: props.rewordInfo?.send_1_gift,
      desc: proxy.$t("stellar_voyage.send_activity_gift"),
      current: props.activityInfo?.daily_tasks?.send_1_gift === 0 ? 0 : 1,
      max: 1,
      status: props.activityInfo?.daily_tasks?.send_1_gift ?? 0,
      go: goRoomTabUrl,
      task_name: "send_1_gift",
    },
  ].filter(Boolean);
});

const weekTasks = computed(() => {
  return [
    {
      rewards: props.rewordInfo?.room_receive_50k,
      desc: proxy.$t("stellar_voyage.stellar_energy", [50000]),
      current: Math.min(props.activityInfo?.room_receive_count ?? 0, 50000),
      max: 50000,
      status: props.activityInfo?.weekly_tasks?.room_receive_50k ?? 0,
      go: goCreateRoomUrl,
      task_name: "room_receive_50k",
    },
    {
      rewards: props.rewordInfo?.room_receive_80k,
      desc: proxy.$t("stellar_voyage.stellar_energy", [80000]),
      current: Math.min(props.activityInfo?.room_receive_count ?? 0, 80000),
      max: 80000,
      status: props.activityInfo?.weekly_tasks?.room_receive_80k ?? 0,
      go: goCreateRoomUrl,
      task_name: "room_receive_80k",
    },
    {
      rewards: props.rewordInfo?.room_receive_150k,
      desc: proxy.$t("stellar_voyage.stellar_energy", [150000]),
      current: Math.min(props.activityInfo?.room_receive_count ?? 0, 150000),
      max: 150000,
      status: props.activityInfo?.weekly_tasks?.room_receive_150k ?? 0,
      go: goCreateRoomUrl,
      task_name: "room_receive_150k",
    },
    {
      rewards: props.rewordInfo?.room_receive_250k,
      desc: proxy.$t("stellar_voyage.stellar_energy", [250000]),
      current: Math.min(props.activityInfo?.room_receive_count ?? 0, 250000),
      max: 250000,
      status: props.activityInfo?.weekly_tasks?.room_receive_250k ?? 0,
      go: goCreateRoomUrl,
      task_name: "room_receive_250k",
    },
    {
      rewards: props.rewordInfo?.room_receive_350k,
      desc: proxy.$t("stellar_voyage.stellar_energy", [350000]),
      current: Math.min(props.activityInfo?.room_receive_count ?? 0, 350000),
      max: 350000,
      status: props.activityInfo?.weekly_tasks?.room_receive_350k ?? 0,
      go: goCreateRoomUrl,
      task_name: "room_receive_350k",
    },
    {
      rewards: props.rewordInfo?.room_receive_500k,
      desc: proxy.$t("stellar_voyage.stellar_energy", [500000]),
      current: Math.min(props.activityInfo?.room_receive_count ?? 0, 500000),
      max: 500000,
      status: props.activityInfo?.weekly_tasks?.room_receive_500k ?? 0,
      go: goCreateRoomUrl,
      task_name: "room_receive_count",
    },
  ];
});

const ruleDialogRef = ref();
const handleRuleDialog = () => {
  ruleDialogRef.value?.open(
    t("stellar_voyage.broadcast_definition"),
    t("stellar_voyage.continuous_broadcast_rule")
  );
};
const handleGo = (url) => {
  proxy.$siyaApp("openSiyaUrl", {
    url: url,
  });
};

const getRewardsByTaskName = (taskName) => {
  return [...dayTasks.value, ...weekTasks.value, ...day2HoursTasks.value].find(
    (i) => i.task_name === taskName
  )?.rewards;
};

const isGetPrizeLoading = ref(false);
const handleReceive = async (taskName) => {
  if (isGetPrizeLoading.value === true) {
    return;
  }
  isGetPrizeLoading.value = true;
  try {
    const res = await activityApi.live_task_receive({
      task_name: taskName,
    });
    if (res.code === 200) {
      const rewards = getRewardsByTaskName(taskName);
      rewordDialogRef.value?.open(rewards);
    }
    emit("refresh");
    isGetPrizeLoading.value = false;
  } catch (e) {
    console.log(e);
    isGetPrizeLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.tasks-block {
  margin-top: px2rem(6);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: px2rem(60);
  .box2 {
    margin-top: px2rem(6);
  }

  .box-title {
    margin-top: px2rem(56);
    display: flex;
    justify-content: center;
  }

  .box-desc {
    margin: 0 auto;
    margin-top: px2rem(14);
    color: #9da8ff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 400;
    line-height: 140%; /* 16.8px */
    width: px2rem(271);
  }

  .box-value {
    margin-top: px2rem(2);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #9da8ff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 400;
    line-height: 140%; /* 16.8px */

    .value-icon {
      width: px2rem(20);
      height: px2rem(20);
      background-image: url("@/assets/images/activity/stellar-voyage/value.png");
      background-size: 100% 100%;
    }
  }
  .rewards {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: px2rem(17);
    margin-top: px2rem(20);
    .reward {
      display: flex;
      align-items: center;
      flex-direction: column;
      width: px2rem(87);
      .cover {
        width: px2rem(70);
        height: px2rem(70);
      }
      .name {
        margin-top: px2rem(12);
        width: px2rem(87);
        color: #ffa19d;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 14px */
      }
    }
    .coin-box {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: px2rem(11);
      .pk-value-icon {
        width: px2rem(18);
        height: px2rem(18);
        background-image: url("@/assets/images/activity/stellar-voyage/value.png");
        background-size: 100% 100%;
      }
      .coin-number {
        color: #fff;
        font-family: "DIN Next W1G";
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 700;
        line-height: 100%; /* 14px */
      }
    }
  }
  .box-count-down {
    margin-top: px2rem(20);
  }

  .sub-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: px2rem(20);
    .qa {
      width: px2rem(12);
      height: px2rem(12);
      background-image: url("@/assets/images/activity/stellar-voyage/qa.png");
      background-size: 100% 100%;
      flex-shrink: 0;
    }
  }

  .task-list {
    width: 100%;
    padding: 0 px2rem(30);
    padding-bottom: px2rem(30);
    margin-top: px2rem(20);
    position: relative;
    z-index: 2;
    .task {
      display: flex;
      align-items: center;
      border-radius: px2rem(10);
      border: px2rem(1) solid rgba(255, 255, 255, 0.28);
      background: linear-gradient(
        90deg,
        rgba(15, 12, 78, 0.29) 0%,
        rgba(2, 0, 43, 0.29) 100%
      );
      margin-bottom: px2rem(10);
      padding: px2rem(10);
      .cover {
        flex-shrink: 0;
        width: px2rem(60);
        height: px2rem(60);
      }
      .detail {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        .detail-desc {
          color: #f5e8ff;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-bottom: px2rem(8);
        }
        .process {
          width: 100%;
          margin-bottom: px2rem(4);
        }
        .process-desc {
          color: rgba(157, 160, 255, 0.6);
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
      .action {
        flex-shrink: 0;
        .action-button {
          min-width: px2rem(56);
          height: px2rem(24);
          color: #ffe3e3;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 700;
          line-height: normal;

          border-radius: px2rem(50);
          border: 1px solid #ffa2a2;
          background: linear-gradient(
            180deg,
            #ffad9e 0%,
            #af1af2 47.12%,
            #56009e 89.9%
          );
          box-shadow: 0px px2rem(3) px2rem(3) 0px rgba(255, 255, 255, 0.26)
            inset;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .action-button.claim {
          color: #a609df;
          background: linear-gradient(180deg, #fff052 1.44%, #ff7c27 100%);
        }
        .action-button.claimed {
          color: #cb8ffe;
          background: linear-gradient(180deg, #5d0ca0 14.08%, #390081 70.67%);
        }
      }
    }
  }
}

.lang-zh_cn,
.lang-zh_tw {
  .box-title {
    -webkit-text-stroke-width: 0 !important;
  }
}
</style>

<template>
  <div class="party-container">
    <header-bar
      back-icon-show
      :show-title="false"
      :padding="false"
      :contentPadding="false"
      close-type="close"
    />
    <div class="title" :data-title="$t('party_poster.title')">
      <div>{{ $t("party_poster.title") }}</div>
    </div>
    <div class="content">
      <div class="box1 mt-91">
        <div class="box1-content">
          <div class="box1-content-text">
            {{ $t("party_poster.intro_desc") }}
          </div>
        </div>
      </div>
      <div class="box1 mt-16">
        <div class="box1-content">
          <div class="table">
            <!-- 表头 -->
            <div class="table-header">
              <div
                class="header-title"
                :data-title="$t('party_poster.raward_1')"
              >
                <div>{{ $t("party_poster.raward_1") }}</div>
              </div>
            </div>
            <!-- 表格内容 -->
            <div class="table-content  table-content-two-columns">
              <!-- 列标题行 -->
              <div class="table-row header-row">
                <div class="table-cell">
                  {{ $t("party_poster.gift_heat_requirements") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.room_owner_rewards") }}
                </div>
              </div>
              <!-- 数据行 -->
              <div class="table-row">
                <div class="table-cell">15000</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/7.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>3</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/1.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>12</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/15.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>2</span></div>
                    </div>
                  </div>
                  <div class="worth">
                    <span>{{ $t("party_poster.worth") }}</span>
                    <gap :gap="2"></gap>
                    <span>600</span>
                    <gap :gap="2"></gap>
                    <gender-coin :size="12"></gender-coin>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <div class="table-cell">30000</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/14.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>1</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/1.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>20</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/15.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>2</span></div>
                    </div>
                  </div>
                  <div class="worth">
                    <span>{{ $t("party_poster.worth") }}</span>
                    <gap :gap="2"></gap>
                    <span>1500</span>
                    <gap :gap="2"></gap>
                    <gender-coin :size="12"></gender-coin>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <div class="table-cell">150000</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/1.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>60</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/16.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>3</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/14.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>3</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/13.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>1</span></div>
                    </div>
                  </div>
                  <div class="worth">
                    <span>{{ $t("party_poster.worth") }}</span>
                    <gap :gap="2"></gap>
                    <span>9000</span>
                    <gap :gap="2"></gap>
                    <gender-coin :size="12"></gender-coin>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <div class="table-cell">300000</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/16.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>5</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/8.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>1</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/14.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>6</span></div>
                    </div>
                  </div>
                  <div class="worth">
                    <span>{{ $t("party_poster.worth") }}</span>
                    <gap :gap="2"></gap>
                    <span>21000</span>
                    <gap :gap="2"></gap>
                    <gender-coin :size="12"></gender-coin>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <div class="table-cell">600000</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/8.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>2</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/16.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>5</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/3.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>2</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/14.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>4</span></div>
                    </div>
                  </div>
                  <div class="worth">
                    <span>{{ $t("party_poster.worth") }}</span>
                    <gap :gap="2"></gap>
                    <span>48000</span>
                    <gap :gap="2"></gap>
                    <gender-coin :size="12"></gender-coin>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <div class="table-cell">1000000</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/8.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>2</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/14.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>2</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/3.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>5</span></div>
                    </div>
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/5.png" /></img>
                      </div>
                      <div class="desc"><span>x</span><span>1</span></div>
                    </div>
                  </div>
                  <div class="worth">
                    <span>{{ $t("party_poster.worth") }}</span>
                    <gap :gap="2"></gap>
                    <span>100000</span>
                    <gap :gap="2"></gap>
                    <gender-coin :size="12"></gender-coin>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二个表格 - Raward 2 -->
      <div class="box1 mt-16">
        <div class="box1-content">
          <div class="table">
            <!-- 表头 -->
            <div class="table-header">
              <div
                class="header-title"
                :data-title="$t('party_poster.raward_2')"
              >
                <div>{{ $t("party_poster.raward_2") }}</div>
              </div>
            </div>
            <!-- 表格内容 -->
            <div class="table-content table-content-merged">
              <!-- 列标题行 -->
              <div class="table-row header-row">
                <div class="table-cell">
                  {{ $t("party_poster.party_requirements") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.room_owner_rewards") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.top_rewards") }}
                </div>
              </div>
              <!-- 使用Grid布局实现合并单元格 -->
              <div class="merged-table-grid">
                <!-- 第一列合并单元格 -->
                <div class="merged-cell-1">
                  <div class="requirement-text">
                    {{ $t("party_poster.requirement_users_20") }}
                  </div>
                  <div class="requirement-text">
                    {{ $t("party_poster.requirement_gift_users_5") }}
                  </div>
                  <div class="requirement-text">
                    {{ $t("party_poster.requirement_heat_30k") }}
                  </div>
                </div>
                <!-- 第二列合并单元格 -->
                <div class="merged-cell-2">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/4.png" /></img>
                      </div>
                      <div style="display: flex;"><span>x</span><span>{{ $t("common.common_day", [1]) }}</span></div>
                    </div>
                  </div>
                </div>
                <!-- 第三列独立单元格 -->
                <div class="individual-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/11.png" /></img>
                      </div>
                      <div style="display: flex;"><span>x</span><span>{{ $t("common.common_day", [1]) }}</span></div>
                    </div>
                  </div>
                  <div>{{ $t("party_poster.title_support_king") }}</div>
                </div>
                <div class="individual-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/12.png" /></img>
                      </div>
                      <div style="display: flex;"><span>x</span><span>{{ $t("common.common_day", [1]) }}</span></div>
                    </div>
                  </div>
                  <div>{{ $t("party_poster.title_supporter") }}</div>
                </div>
                <div class="individual-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/9.png" /></img>
                      </div>
                      <div style="display: flex;"><span>x</span><span>{{ $t("common.common_day", [1]) }}</span></div>
                    </div>
                  </div>
                  <div>{{ $t("party_poster.title_charm_king") }}</div>
                </div>
                <div class="individual-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cover">
                        <img src="@/assets/images/activity/poster/party/rewards/10.png" /></img>
                      </div>
                      <div style="display: flex;"><span>x</span><span>{{ $t("common.common_day", [1]) }}</span></div>
                    </div>
                  </div>
                  <div>{{ $t("party_poster.title_fascination") }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 注释文本 -->
      <div class="note-text">
        {{ $t("party_poster.note", [2]) }}
      </div>

      <!-- 第三个表格 - Extra reward -->
      <div class="box1 mt-16">
        <div class="box1-content">
          <div class="table">
            <!-- 表头 -->
            <div class="table-header">
              <div
                class="header-title"
                :data-title="$t('party_poster.extra_reward')"
              >
                <div>{{ $t("party_poster.extra_reward") }}</div>
              </div>
            </div>
            <!-- 表格内容 -->
            <div class="table-content table-content-two-columns">
              <!-- 列标题行 -->
              <div class="table-row header-row">
                <div class="table-cell">{{ $t("party_poster.qualified_parties_count") }}</div>
                <div class="table-cell">{{ $t("party_poster.room_owner_rewards") }}</div>
              </div>
              <!-- 数据行 -->
              <div class="table-row">
                <div class="table-cell">3</div>
                <div class="table-cell">
                  <div class="rewards">
                    <div class="reward-item">
                      <div class="cheng-hao">
                        Party Star
                      </div>
                      <div style="display: flex;"><span>x</span><span>{{ $t("common.common_days", [7]) }}</span></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部注释文本 -->
      <div class="note-text">
        {{ $t("party_poster.note_extra_title") }}
      </div>

      <!-- Party play guide 部分 -->
      <div class="box1 mt-16">
        <div class="box1-content">
          <!-- 表头 -->
          <div class="box4-header">
            <span>{{ $t("party_poster.play_guide_title") }}</span>
          </div>
          <div class="guide-container">
            <!-- Talent Party 部分 -->
            <div class="party-section">
              <div class="party-title">
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
                <span>{{ $t("party_poster.talent_party_title") }}</span>
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.talent_mic_allocation") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.talent_mic_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.talent_competition_process") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.talent_competition_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.talent_interactive_voting") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.talent_voting_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.talent_promotion_elimination") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.talent_elimination_desc") }}
                </div>
              </div>
            </div>

            <!-- PK Party 部分 -->
            <div class="party-section">
              <div class="party-title">
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
                <span>{{ $t("party_poster.pk_party_title") }}</span>
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.pk_open_mode") }}</div>
                <div class="guide-text">{{ $t("party_poster.pk_mode_desc") }}</div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.pk_process") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.pk_process_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.pk_result_announcement") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.pk_result_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.pk_interesting_interaction") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.pk_interaction_desc") }}
                </div>
              </div>
            </div>

            <!-- Game Party 部分 -->
            <div class="party-section">
              <div class="party-title">
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
                <span>{{ $t("party_poster.game_party_title") }}</span>
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.game_mic_allocation") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.game_mic_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.game_process") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.game_process_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.game_interaction_method") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.game_interaction_desc") }}
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">{{ $t("party_poster.game_interaction_method") }}</div>
                <div class="guide-text">
                  {{ $t("party_poster.game_interaction_desc") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup></script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.party-container {
  position: relative;
  min-height: 100vh;
  background-image: url("@/assets/images/activity/poster/party/bg-1.png");
  background-size: 100% px2rem(394);
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: px2rem(10);
    left: px2rem(-76);
    width: px2rem(488);
    height: px2rem(293);
    background-image: url("@/assets/images/activity/poster/party/bg-2.png");
    background-size: px2rem(488) px2rem(293);
    background-repeat: no-repeat;
  }

  .title {
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(34);
    font-style: italic;
    font-weight: 900;
    line-height: px2rem(34); /* 100% */
    letter-spacing: px2rem(-0.32);
    position: relative;
    z-index: 2;
    margin-top: px2rem(36);

    z-index: 0;
    div {
      color: #fff;
    }
    &::after {
      content: attr(data-title);
      text-shadow: px2rem(0) px2rem(4) px2rem(15.2) #f00;
      -webkit-text-stroke-width: px2rem(2);
      -webkit-text-stroke-color: #fb00ff;
      text-align: center;
      position: absolute;
      inset: 0;
      z-index: -1;
    }
  }

  .content {
    margin-top: px2rem(200);
    width: 100%;
    flex-shrink: 0;
    background: linear-gradient(
      180deg,
      rgba(183, 89, 220, 0) 0.75%,
      #b659db 3.23%,
      #d569ff 100%
    );
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: px2rem(52);

    .box1 {
      width: px2rem(363);
      flex-shrink: 0;
      border-radius: px2rem(17);
      border: px2rem(1) solid #fff;
      background: rgba(255, 255, 255, 0.25);
      padding: px2rem(5);
      .box1-content {
        border-radius: px2rem(12);
        background: #bc35f2;

        color: #fff;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        overflow: visible;

        .box1-content-text {
          padding: px2rem(13);
        }
      }
    }
  }
}
.mt-91 {
  margin-top: px2rem(91);
}
.mt-16 {
  margin-top: px2rem(16);
}
.table {
  border-radius: px2rem(12);
  border: px2rem(1) solid #fff;

  .table-header {
    width: 100%;
    height: px2rem(43);
    flex-shrink: 0;
    background: linear-gradient(90deg, #edd9fd 0%, #ff9af2 100%);
    border-radius: px2rem(11) px2rem(11) 0 0;
    border-bottom: px2rem(1) solid #fff;

    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 px2rem(15);
    position: relative;

    .header-title {
      z-index: 2;
      position: relative;
      font-family: Gilroy;
      font-size: px2rem(24);
      font-style: italic;
      font-weight: 900;
      line-height: normal;

      z-index: 0;
      div {
        color: #bf29fb;
      }
      &::after {
        content: attr(data-title);
        -webkit-text-stroke-width: px2rem(2);
        -webkit-text-stroke-color: #fff;
        text-align: center;
        position: absolute;
        inset: 0;
        z-index: -1;
      }
    }

    &::before,
    &::after {
      position: absolute;
      content: "";
      background-image: url("@/assets/images/activity/poster/party/star.png");
      background-size: 100% 100%;
      z-index: 1;
    }
    &::before {
      width: px2rem(57);
      height: px2rem(63);
      top: px2rem(-10);
      right: px2rem(-14);
    }
    &::after {
      width: px2rem(82);
      height: px2rem(95);
      top: px2rem(-29);
      right: px2rem(5);
      transform: rotate(-10.051deg);
    }
  }

  .table-content {
    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      border-bottom: px2rem(1) solid #fff;

      &:last-child {
        border-bottom: none;
      }

      &.header-row {
        border-bottom: px2rem(1) solid #fff;

        .table-cell {
          font-weight: 700;
          font-size: px2rem(14);
          color: #fff;
          text-shadow: 0 px2rem(1) px2rem(2) rgba(0, 0, 0, 0.3);
        }
      }

      .table-cell {
        padding: px2rem(16) px2rem(4);
        color: rgba(255, 255, 255, 0.95);
        font-family: Gilroy;
        font-size: px2rem(12);
        font-weight: 500;
        line-height: 1.3;
        text-align: center;
        border-right: px2rem(1) solid #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: px2rem(50);

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  // 合并单元格Grid布局样式
  .table-content-merged {
    .table-row {
      grid-template-columns: 1fr 0.9fr 1fr;
    }
    .merged-table-grid {
      display: grid;
      grid-template-columns: 1fr 0.9fr 1fr;
      grid-template-rows: repeat(3, 1fr);
      min-height: px2rem(120);

      .merged-cell-1 {
        grid-column: 1;
        grid-row: 1 / 5;
        border-right: px2rem(1) solid #fff;
        padding: px2rem(15);
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .requirement-text {
          color: #fff;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }

      .merged-cell-2 {
        grid-column: 2;
        grid-row: 1 / 5;
        border-right: px2rem(1) solid #fff;
        padding: px2rem(15);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        justify-content: space-around;

        .reward-text {
          color: #fff;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }

      .individual-cell {
        grid-column: 3;
        border-bottom: px2rem(1) solid #fff;
        padding: px2rem(15) 0;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: #fff;
        font-family: Gilroy;
        font-size: px2rem(11);
        font-style: normal;
        font-weight: 500;
        line-height: normal;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .bottom-note {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(11);
    font-weight: 400;
    line-height: 1.4;
    margin: px2rem(20) px2rem(20) px2rem(40);
    padding: px2rem(15);
    background: rgba(255, 255, 255, 0.1);
    border-radius: px2rem(10);
    border: px2rem(1) solid rgba(255, 255, 255, 0.2);
  }

  // 两列表格样式
  .table-content-two-columns {
    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr;

      .table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
    }
  }
}
.note-text {
  color: #a133cd;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(13);
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin: px2rem(6) auto;
  width: px2rem(352);
}

// Party play guide 样式
.guide-container {
  padding: px2rem(12);
}

.guide-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: px2rem(20);

  .guide-title {
    color: #fff;
    font-size: px2rem(20);
    font-weight: bold;
    margin: 0 px2rem(10);
    font-family: Gilroy;
  }

  .star-icon {
    width: px2rem(26);
    height: px2rem(26);

    &.left {
      margin-right: px2rem(10);
    }

    &.right {
      margin-left: px2rem(10);
    }
  }
}

.party-section {
  margin-bottom: px2rem(30);

  .party-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: px2rem(15);

    span {
      color: #fff;
      text-align: center;
      text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(18);
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 23.4px */
      padding: 0 px2rem(8);
    }

    .star-icon {
      width: px2rem(26);
      height: px2rem(26);
    }
  }

  .guide-item {
    margin-bottom: px2rem(12);

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .guide-label {
      display: flex;
      padding: px2rem(3) px2rem(11);
      justify-content: center;
      align-items: center;
      color: #fff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(11);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      border-radius: px2rem(22);
      background: #ff871f;
    }

    .guide-text {
      color: rgba(255, 255, 255, 0.8);
      font-family: Gilroy;
      font-size: px2rem(12);
      text-align: left;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 15.6px */
      margin-top: px2rem(4);
    }
  }
}

.box4-header {
  width: 100%;
  height: px2rem(43);
  flex-shrink: 0;
  background: linear-gradient(90deg, #edd9fd 0%, #ff9af2 100%);
  border-radius: px2rem(11) px2rem(11) 0 0;
  border-bottom: px2rem(1) solid #fff;

  color: #bf29fb;
  -webkit-text-stroke-width: px2rem(1);
  -webkit-text-stroke-color: #fff;
  font-family: Gilroy;
  font-size: px2rem(24);
  font-style: italic;
  font-weight: 900;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 px2rem(15);
  position: relative;

  span {
    z-index: 2;
    position: relative;
  }
}
.rewards {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  .reward-item {
    display: flex;
    align-items: center;
    gap: 4px;
    .cover {
      img {
        width: px2rem(20);
        height: px2rem(20);
      }
    }
    .desc {
      color: #fff;
      display: flex;
    }
  }
}

.cheng-hao {
  width: px2rem(115);
  height: px2rem(30);
  background-image: url("@/assets/images/activity/poster/party/rewards/17.png");
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding-left: px2rem(34);
  font-size: px2rem(11);
}

.worth {
  display: flex;
  align-items: center;
  font-size: px2rem(10);
}

.lang-ar {
  .table-header {
    &::before {
      right: unset;
      left: px2rem(-14);
    }
    &::after {
      right: unset;
      left: px2rem(5);
    }
  }
    .table-cell {
        border-right: none !important;
        border-left: px2rem(1) solid #fff !important;
        &:last-child {
          border-left: none !important;
        }
    }
    .merged-cell-1 {
        border-left: px2rem(1) solid #fff !important;
        border-right: none !important;
    }
    .merged-cell-2{
        border-left: px2rem(1) solid #fff !important;
        border-right: none !important;
    }
    .guide-text {
      text-align: right !important;
    }
    .cheng-hao {
      padding-right: px2rem(34);
      padding-left: 0;
    }
}

</style>

<template>
  <div class="container">
    <div class="bg-box">
      <header-bar
        back-icon-show
        :show-title="true"
        :content-padding="false"
        :title="$t('stellar_voyage.stellar_voyage')"
        close-type="back"
        is-scroll-change-bg
        :always-show-right-icon="true"
        @emit:scrollChangeBgShow="scrollChangeBgShow"
      >
        <template #right>
          <svg-icon
            class="icon-concat"
            :icon="showPageTitle ? 'contact-black' : 'contact-white'"
            :color="showPageTitle ? '#000' : '#fff'"
            @click="goFeedback()"
            v-track:click
            trace-key="active_button_click"
            :track-params="
              JSON.stringify({
                active_button_click_name: '2',
                active_name: 'Stellar Voyage-房主开播激励活动',
              })
            "
          />
        </template>
      </header-bar>

      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>

      <div class="count-down">
        <CountDown
          :left-time="
            (activityInfo?.week_countdown || 0) - activityInfo?.requestTime
          "
          :style-type="1"
        ></CountDown>
      </div>
    </div>

    <div class="content">
      <div class="tabs">
        <div
          class="tab"
          :class="current === 0 ? 'active' : ''"
          @click="current = 0"
        >
          <span>{{ $t("stellar_voyage.rewards") }}</span>
        </div>
        <gap :gap="12"></gap>
        <div
          class="tab"
          :class="current === 1 ? 'active' : ''"
          @click="current = 1"
        >
          <span>{{ $t("stellar_voyage.rules") }}</span>
        </div>
      </div>
      <RuleRewards
        v-show="current === 0"
        v-if="activityInfo && rewordInfo"
        :activityInfo="activityInfo"
        :rewordInfo="rewordInfo"
      ></RuleRewards>
      <RuleContent
        v-show="current === 1"
        v-if="activityInfo && rewordInfo"
        :timeZone="activityInfo?.timezone"
      ></RuleContent>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { getLang } from "@/i18n/index.js";
import RuleRewards from "./components/RuleRewards.vue";
import RuleContent from "./components/RuleContent.vue";
import CountDown from "./components/CountDown.vue";
import { useRouter, useRoute } from "vue-router";
import activityApi from "@/api/activity.js";

const router = useRouter();
const route = useRoute();
const { from } = route.query;
const current = ref(0);
const lang = getLang();
const activityInfo = ref();
const rewordInfo = ref();

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/stellar-voyage/${lang}/${name}`,
    import.meta.url
  ).href;
}
const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};

const showPageTitle = ref(false);
const scrollChangeBgShow = (show) => {
  showPageTitle.value = show;
};

const requestTime = ref(0);
const fetchActivityInfo = async () => {
  try {
    requestTime.value = 0;
    const now = Date.now();
    const res = await activityApi.live_info();
    requestTime.value = Math.floor((Date.now() - now) / 1000);
    if (res.code === 200) {
      activityInfo.value = {
        ...res.data,
        requestTime: requestTime.value,
      };
    }
  } catch (e) {
    console.error(e);
  }
};

const fetchRewardsInfo = async () => {
  try {
    const res = await activityApi.live_rewards();
    if (res.code === 200) {
      rewordInfo.value = res.data;
    }
  } catch (e) {
    console.error(e);
  }
};

fetchActivityInfo();
fetchRewardsInfo();
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  .bg-box {
    background-image: url("@/assets/images/activity/stellar-voyage/bg1.png");
    background-repeat: no-repeat;
    background-size: 100% px2rem(336);
    width: 100%;
    height: px2rem(336);
    overflow: hidden;
    .banner-title {
      width: 100%;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(233);
      img {
        opacity: 0;
        width: 100%;
        vertical-align: bottom;
      }
    }
    .count-down {
      margin: 0 auto;
      margin-top: px2rem(-16);
    }
  }

  .content {
    background-image: url("@/assets/images/activity/stellar-voyage/bg2.png");
    background-repeat: repeat-y;
    background-size: 100% px2rem(343);
    overflow: auto;
    margin-top: -1px;
  }

  .tabs {
    width: 100%;
    padding: 0 px2rem(16);
    margin-top: px2rem(8);
    display: flex;
    position: relative;
    z-index: 1;
    .tab {
      width: px2rem(174);
      height: px2rem(55);
      padding: 0 px2rem(10);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/stellar-voyage/button11.png");
      background-repeat: no-repeat;
      background-size: px2rem(154) px2rem(38);
      background-position: center center;

      color: #8284da;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 18.6px */
    }

    .tab.active {
      background-image: url("@/assets/images/activity/stellar-voyage/button1.png");
      background-size: 100% 100%;
      span {
        color: #fff;
        text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
        font-weight: 700;
      }
    }
  }
}

.icon-concat {
  font-size: px2rem(28);
  color: unset;
}
</style>

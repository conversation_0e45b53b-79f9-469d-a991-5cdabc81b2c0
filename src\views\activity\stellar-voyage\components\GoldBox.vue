<template>
  <div class="gb-outer-border" :style="style">
    <div class="bg">
      <div v-if="showDecoration" class="r-1"></div>
      <div v-if="showDecoration" class="r-2"></div>
      <div v-if="showDecoration" class="r-3"></div>
    </div>
    <div class="gb-inside-border">
      <div class="gb-inner" :class="showBg ? 'show-bg' : ''">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  showBg: {
    type: Boolean,
    default: true,
  },
  showDecoration: {
    type: Boolean,
    default: true,
  },
  borderRadius: {
    type: Number,
    default: [40, 40, 40, 40],
  },
});

const style = computed(() => {
  return {
    "--border-radius": `${props.borderRadius
      .map((i) => `${proxy.$pxToRemPx(i)}px`)
      .join(" ")}`,
  };
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.gb-outer-border {
  width: 100%;
  height: fit-content;
  min-height: px2rem(370);
  position: relative;
  .bg {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    z-index: 2;
  }
  .r-1,
  .r-2,
  .r-3 {
    width: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: top left;
  }
  .r-1,
  .r-3 {
    height: px2rem(185);
  }
  .r-1 {
    background-image: url("@/assets/images/activity/stellar-voyage/bg3.png");
  }
  .r-2 {
    background-image: url("@/assets/images/activity/stellar-voyage/bg4.png");
    background-repeat: repeat-y;
    flex-grow: 1;
    margin-top: -1px;
    margin-bottom: -1px;
  }
  .r-3 {
    background-image: url("@/assets/images/activity/stellar-voyage/bg5.png");
  }
  .gb-inside-border {
    position: relative;
    z-index: 3;
    .gb-inner {
      overflow: auto;
    }
  }
}
</style>

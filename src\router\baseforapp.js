const routes = [
  /* ===== 任务中心说明 ===== */
  {
    path: "/task-center/rule",
    name: "TaskCenterRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "TaskCenterRule"*/ "../views/baseforapp/task-center/Rule.vue"
      ),
  },

  /* ===== 收不到验证码 ===== */
  {
    path: "/no-verification-code",
    name: "NoVerificationCode",
    component: () =>
      import(
        /*webChunkName: "NoVerificationCode"*/ "../views/baseforapp/no-verification-code/Index.vue"
      ),
  },

  /* ===== 聊天小技巧 ===== */
  {
    path: "/chat-tips",
    name: "ChatTips",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "ChatTips"*/ "../views/baseforapp/chat-tips/Index.vue"
      ),
  },

  /* ===== 如何赚钱 ===== */
  {
    path: "/how-to-earn",
    name: "HowToEarn",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "HowToEarn"*/ "../views/baseforapp/welcome/HowToEarn.vue"
      ),
  },

  /* ===== 如何脱单 ===== */
  {
    path: "/how-to-meet",
    name: "HowToMeet",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "HowToMeet"*/ "../views/baseforapp/welcome/HowToMeet.vue"
      ),
  },

  /* ===== 处罚详情 ===== */
  {
    path: "/banning-detail",
    name: "BanningDetail",
    component: () =>
      import(
        /*webChunkName: "BanningDetail"*/ "../views/baseforapp/banning-detail/Index.vue"
      ),
  },

  /* ===== 常驻榜单说明 ===== */
  {
    path: "/daily-ranking/rule",
    name: "DailyRankingRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "DailyRankingRule"*/ "../views/baseforapp/daily-ranking/Rule2.vue"
      ),
  },

  /* ===== 称号墙===== */
  {
    path: "/designation-wall",
    name: "DesignationWall",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "DesignationWall"*/ "../views/baseforapp/designation-wall/Index.vue"
      ),
  },

  /* ===== 勋章墙===== */
  {
    path: "/modal-wall",
    name: "ModalWall",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "ModalWall"*/ "../views/baseforapp/medal-wall/Index.vue"
      ),
  },
  /* ===== 海报===== */
  {
    path: "/poster",
    name: "poster",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "DailyRankingRule"*/ "../views/baseforapp/poster/index.vue"
      ),
  },
  /* ===== 海报-家族PK===== */
  {
    path: "/poster/pk",
    name: "posterPk",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "posterPk"*/ "../views/baseforapp/poster/FamilyPk.vue"
      ),
  },
  /* ===== 宣传片 ===== */
  {
    path: "/promotional",
    name: "Promotional",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "Promotional"*/ "../views/baseforapp/promotional/Index.vue"
      ),
  },
  {
    path: "/siya/gottalent-2",
    name: "PromotionalVideo",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PromotionalVideo"*/ "../views/baseforapp/promotional/videoPage.vue"
      ),
  },
  {
    path: "/siya/sgt2contenders",
    name: "PromotionalVideo2",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "PromotionalVideo2"*/ "../views/baseforapp/promotional/videoPage2.vue"
      ),
  },
  // 爱情秘籍
   {
    path: "/education/love-secret",
    name: "EducationLoveSecret",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "EducationLoveSecret"*/ "../views/baseforapp/education/love-secret/Index.vue"
      ),
  },
  // 错失交友的行为
  {
    path: "/education/miss-love",
    name: "EducationMissLove",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "EducationMissLove"*/ "../views/baseforapp/education/miss-love/Index.vue"
      ),
  },
];

export default routes;

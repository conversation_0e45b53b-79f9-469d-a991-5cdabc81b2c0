<template>
  <div class="container">
    <right-button :text="$t('stellar_voyage.rules')"></right-button>
    <div
      class="bg-box"
      v-track:exposure
      trace-key="active_exposure"
      :track-params="
        JSON.stringify({
          active_exposure_source: from,
          active_name: 'Stellar Voyage-房主开播激励活动',
        })
      "
    >
      <header-bar
        back-icon-show
        :padding="false"
        :content-padding="false"
        :show-title="false"
        close-type="close"
      />

      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>
      <div class="count-down">
        <CountDown
          :left-time="
            (activityInfo?.week_countdown || 0) - activityInfo?.requestTime
          "
          :style-type="1"
        ></CountDown>
      </div>
    </div>
    <div class="content">
      <div class="tabs">
        <div
          class="tab"
          :class="current === 0 ? 'active' : ''"
          @click="current = 0"
        >
          <span>{{ $t("stellar_voyage.voyage_tasks") }}</span>
        </div>
        <div
          class="tab"
          :class="current === 1 ? 'active' : ''"
          @click="current = 1"
        >
          <span>{{ $t("stellar_voyage.starfield_rankings") }}</span>
        </div>
      </div>
      <TasksBlock
        v-show="current === 0"
        :activityInfo="activityInfo"
        :rewordInfo="rewordInfo"
        @refresh="fetchActivityInfo()"
      ></TasksBlock>
      <RoomRankingBlock
        v-show="current === 1"
        :activityInfo="activityInfo"
        :rewordInfo="rewordInfo"
      ></RoomRankingBlock>
    </div>
  </div>
  <RewordDialog ref="rewordDialogRef"></RewordDialog>
</template>
<script setup>
import { ref } from "vue";
import { getLang } from "@/i18n/index.js";
import RightButton from "./components/RightButton.vue";
import TasksBlock from "./components/TasksBlock.vue";
import RoomRankingBlock from "./components/RoomRankingBlock.vue";
import CountDown from "./components/CountDown.vue";
import RewordDialog from "./components/RewordDialog.vue";
import { useRouter, useRoute } from "vue-router";
import activityApi from "@/api/activity.js";

const router = useRouter();
const route = useRoute();
const { from } = route.query;
const rewordDialogRef = ref();
const current = ref(0);
const lang = getLang();
const activityInfo = ref();
const rewordInfo = ref();

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/stellar-voyage/${lang}/${name}`,
    import.meta.url
  ).href;
}

const requestTime = ref(0);
const fetchActivityInfo = async () => {
  try {
    requestTime.value = 0;
    const now = Date.now();
    const res = await activityApi.live_info();
    requestTime.value = Math.floor((Date.now() - now) / 1000);
    if (res.code === 200) {
      activityInfo.value = {
        ...res.data,
        requestTime: requestTime.value,
      };
    }
  } catch (e) {
    console.error(e);
  }
};

const fetchRewardsInfo = async () => {
  try {
    const res = await activityApi.live_rewards();
    if (res.code === 200) {
      rewordInfo.value = res.data;
    }
  } catch (e) {
    console.error(e);
  }
};

fetchActivityInfo();
fetchRewardsInfo();
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  .bg-box {
    background-image: url("@/assets/images/activity/stellar-voyage/bg1.png");
    background-repeat: no-repeat;
    background-size: 100% px2rem(336);
    width: 100%;
    height: px2rem(336);
    overflow: hidden;
    .banner-title {
      width: 100%;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(233);
      img {
        opacity: 0;
        width: 100%;
        vertical-align: bottom;
      }
    }
    .count-down {
      margin: 0 auto;
      margin-top: px2rem(-16);
    }
  }

  .content {
    background-image: url("@/assets/images/activity/stellar-voyage/bg2.png");
    background-repeat: repeat-y;
    background-size: 100% px2rem(343);
    overflow: auto;
    margin-top: -1px;
  }

  .tabs {
    width: 100%;
    padding: 0 px2rem(16);
    margin-top: px2rem(8);
    display: flex;
    position: relative;
    z-index: 1;
    .tab {
      width: px2rem(174);
      height: px2rem(55);
      padding: 0 px2rem(10);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/stellar-voyage/button11.png");
      background-repeat: no-repeat;
      background-size: px2rem(154) px2rem(38);
      background-position: center center;

      color: #8284da;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 18.6px */
    }

    .tab.active {
      background-image: url("@/assets/images/activity/stellar-voyage/button1.png");
      background-size: 100% 100%;
      span {
        color: #fff;
        text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
        font-weight: 700;
      }
    }
  }
}
</style>

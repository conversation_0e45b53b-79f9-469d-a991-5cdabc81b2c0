<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_30534_49638)">
<g filter="url(#filter0_d_30534_49638)">
<path d="M6.24984 3.33203C3.71853 3.33203 1.6665 5.38407 1.6665 7.91536C1.6665 12.4987 7.08317 16.6654 9.99984 17.6346C12.9165 16.6654 18.3332 12.4987 18.3332 7.91536C18.3332 5.38407 16.2811 3.33203 13.7498 3.33203C12.1997 3.33203 10.8293 4.10157 9.99984 5.27945C9.17038 4.10157 7.79996 3.33203 6.24984 3.33203Z" fill="url(#paint0_linear_30534_49638)"/>
<path d="M6.24984 3.33203C3.71853 3.33203 1.6665 5.38407 1.6665 7.91536C1.6665 12.4987 7.08317 16.6654 9.99984 17.6346C12.9165 16.6654 18.3332 12.4987 18.3332 7.91536C18.3332 5.38407 16.2811 3.33203 13.7498 3.33203C12.1997 3.33203 10.8293 4.10157 9.99984 5.27945C9.17038 4.10157 7.79996 3.33203 6.24984 3.33203Z" stroke="url(#paint1_linear_30534_49638)" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_30534_49638" x="0.0415039" y="2.70703" width="19.9165" height="17.5508" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.475634 0 0 0 0 0.187223 0 0 0 0 0.519231 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_30534_49638"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_30534_49638" result="shape"/>
</filter>
<linearGradient id="paint0_linear_30534_49638" x1="9.99984" y1="3.33203" x2="9.99984" y2="17.6346" gradientUnits="userSpaceOnUse">
<stop stop-color="#9ECBFF"/>
<stop offset="1" stop-color="#FF88E1"/>
</linearGradient>
<linearGradient id="paint1_linear_30534_49638" x1="9.99984" y1="3.33203" x2="9.99984" y2="17.6346" gradientUnits="userSpaceOnUse">
<stop stop-color="#9ECBFF"/>
<stop offset="1" stop-color="#FF88E1"/>
</linearGradient>
<clipPath id="clip0_30534_49638">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>

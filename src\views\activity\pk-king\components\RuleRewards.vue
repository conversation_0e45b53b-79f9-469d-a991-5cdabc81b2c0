<template>
  <div class="rule-rewards">
    <div class="rule-rewards-tabs">
      <div
        class="rule-rewards-tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        <span>{{ $t("pk_king.task_rewards") }}</span>
      </div>
      <gap :gap="4"></gap>
      <div
        class="rule-rewards-tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
      >
        <span>{{ $t("pk_king.pk_value_ranking") }}</span>
      </div>
      <gap :gap="4"></gap>
      <div
        class="rule-rewards-tab"
        :class="current === 2 ? 'active' : ''"
        @click="current = 2"
      >
        <span>{{ $t("pk_king.pk_contribution_ranking") }}</span>
      </div>
      <gap :gap="4"></gap>
      <div
        class="rule-rewards-tab"
        :class="current === 3 ? 'active' : ''"
        @click="current = 3"
      >
        <span>{{ $t("pk_king.room_ranking") }}</span>
      </div>
    </div>
    <div class="rule-rewards-block">
      <div v-show="current === 0" class="reward-box">
        <GoldBox>
          <div class="rewards">
            <div class="rewards-list">
              <template v-for="(item, index) in taskRewards || []">
                <div class="r">
                  <div class="r-cover">
                    <PrizeBox :rewards="item"></PrizeBox>
                  </div>
                  <div class="r-name">{{ item.reward_name }}</div>
                </div>
                <gap v-if="index > 0" :gap="2"></gap>
              </template>
            </div>
          </div>
        </GoldBox>
      </div>
      <div v-show="current === 1" class="reward-box">
        <GoldBox>
          <div class="rewards">
            <div class="rewards-top-1">
              <div class="top1-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP1">
                  <div>TOP1</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top1 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-2">
              <div class="top2-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP2">
                  <div>TOP2</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top2 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-3">
              <div class="top3-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP3">
                  <div>TOP3</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top3 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-4">
              <div class="top4-tag">
                <div class="tag-content" data-title="TOP4-TOP10">
                  <div>TOP4-TOP10</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top410 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <div v-show="current === 2" class="reward-box">
        <GoldBox>
          <div class="rewards">
            <div class="rewards-top-1">
              <div class="top1-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP1">
                  <div>TOP1</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top?.top1 ||
                  []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-2">
              <div class="top2-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP2">
                  <div>TOP2</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top?.top2 ||
                  []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-3">
              <div class="top3-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP3">
                  <div>TOP3</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top?.top3 ||
                  []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-4">
              <div class="top4-tag">
                <div class="tag-content" data-title="TOP4-TOP10">
                  <div>TOP4-TOP10</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top
                    ?.top410 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <div v-show="current === 3" class="reward-box">
        <GoldBox>
          <div class="rewards">
            <div class="rewards-top-1">
              <div class="top1-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP1">
                  <div>TOP1</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.room_pk_top?.top1 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-2">
              <div class="top2-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP2">
                  <div>TOP2</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.room_pk_top?.top2 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-3">
              <div class="top3-tag">
                <div class="tag-image"></div>
                <div class="tag-content" data-title="TOP3">
                  <div>TOP3</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.room_pk_top?.top3 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-4">
              <div class="top4-tag">
                <div class="tag-content" data-title="TOP4-TOP10">
                  <div>TOP4-TOP10</div>
                </div>
              </div>
              <div class="top-rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.room_pk_top?.top410 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref } from "vue";
import GoldBox from "./GoldBox.vue";
import PrizeBox from "./PrizeBox.vue";
const props = defineProps({
  activityInfo: {
    type: Object,
    default: () => ({}),
  },
  rewordInfo: {
    type: Object,
    default: () => ({}),
  },
});
const current = ref(0);
const taskRewards = computed(() => {
  return [
    ...props.rewordInfo?.pk1000,
    ...props.rewordInfo?.pk_win,
    ...props.rewordInfo?.pk_contribution1000,
    ...props.rewordInfo?.pk_room_live10,
    ...props.rewordInfo?.pk_room5000,
    ...props.rewordInfo?.pk_room_online_num5,
    ...props.rewordInfo?.pk50k,
    ...props.rewordInfo?.pk100k,
    ...props.rewordInfo?.pk200k,
    ...props.rewordInfo?.room_pk100k,
    ...props.rewordInfo?.room_pk200k,
    ...props.rewordInfo?.room_pk500k,
    ...props.rewordInfo?.contribution50k,
    ...props.rewordInfo?.contribution100k,
    ...props.rewordInfo?.contribution200k,
  ];
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.rule-rewards {
  margin-top: px2rem(12);
  .rule-rewards-tabs {
    width: 100%;
    padding: 0 px2rem(4);
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
    .rule-rewards-tab {
      width: px2rem(87);
      height: px2rem(39);
      padding: 0 px2rem(3);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/pk-king/button33.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      color: #deb8ff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(10);
      font-style: normal;
      font-weight: 500;
      line-height: 120%;
    }

    .rule-rewards-tab.active {
      background-image: url("@/assets/images/activity/pk-king/button3.png");
      span {
        color: #ffefbc;
        font-weight: 700;
      }
    }
  }
}
.rule-rewards-block {
  padding-bottom: px2rem(80);
}
.reward-box {
  width: 100%;
  margin-top: px2rem(50);
  padding: 0 px2rem(11);
  .rewards {
    padding-top: px2rem(70);
    padding-bottom: px2rem(25);
    .rewards-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      flex-grow: 1;
      width: px2rem(275);
    }

    .r {
      width: px2rem(87);
      padding-bottom: px2rem(25);
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      .r-cover {
        width: px2rem(70);
        height: px2rem(70);
      }
      .r-name {
        margin-top: px2rem(6);
        color: #eb9dff;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 14px */
      }
    }
  }
}

.rewards-top-1,
.rewards-top-2,
.rewards-top-3,
.rewards-top-4 {
  width: px2rem(332);
  min-height: px2rem(118);
  padding-top: px2rem(22);
  margin-bottom: px2rem(16);
  flex-shrink: 0;
  border-radius: px2rem(8);
  border: 0.6px solid #ff0;
  background: rgba(255, 255, 255, 0.1);
  /* 边框1 */
  box-shadow: 0px 0px px2rem(24) 0px #ffe100 inset;

  display: flex;
  flex-direction: column;
  align-items: center;

  .top1-tag {
    .tag-image {
      background-image: url("@/assets/images/activity/pk-king/top1-tag.png");
    }
    .tag-content {
      div {
        background: linear-gradient(
          96deg,
          #fb0 2.46%,
          #ffe6a3 31.48%,
          #fabb01 63.11%,
          #ffe9ad 98.59%
        );
      }
    }
  }

  .top2-tag {
    .tag-image {
      background-image: url("@/assets/images/activity/pk-king/top2-tag.png");
    }
    .tag-content {
      div {
        background: linear-gradient(
          97deg,
          #729fff 1.66%,
          #c5d8ff 36.34%,
          #709cfb 66.74%,
          #aec8ff 100.47%
        );
      }
    }
  }

  .top3-tag {
    .tag-image {
      background-image: url("@/assets/images/activity/pk-king/top3-tag.png");
    }
    .tag-content {
      div {
        background: linear-gradient(
          97deg,
          #c25e00 4.24%,
          #ff9e43 35.08%,
          #c15d01 64.57%,
          #ff830f 100.08%
        );
      }
    }
  }

  .top1-tag,
  .top2-tag,
  .top3-tag {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: px2rem(7);
    margin-bottom: px2rem(20);
    .tag-image {
      width: px2rem(32);
      height: px2rem(22);
      flex-shrink: 0;
      background-size: 100% 100%;
    }
    .tag-content {
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 900;
      line-height: 100%; /* 14px */
      position: relative;
      z-index: 0;
      div {
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &::after {
        content: attr(data-title);
        text-align: center;
        -webkit-text-stroke-width: px2rem(2);
        -webkit-text-stroke-color: #5f0297;
        position: absolute;
        inset: 0;
        z-index: -1;
      }
    }
  }

  .top-rewards-list {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
    width: px2rem(275);
  }

  .r {
    width: px2rem(87);
    padding-bottom: px2rem(12);
    display: flex;
    flex-direction: column;
    align-items: center;
    .r-cover {
      width: px2rem(70);
      height: px2rem(70);
    }
    .r-name {
      margin-top: px2rem(6);
      color: #eb9dff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 14px */
    }
  }
}

.rewards-top-2 {
  border: 0.6px solid #729fff;
  box-shadow: 0px 4px 24px 0px #729fff inset;
}

.rewards-top-3 {
  border: 0.6px solid #c25e00;
  box-shadow: 0px 4px 24px 0px #c25e00 inset;
}

.rewards-top-4 {
  border: 1px solid rgba(255, 255, 255, 0.7);
  box-shadow: unset;
  margin-bottom: px2rem(22);

  .top4-tag {
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 900;
    line-height: 100%; /* 14px */
    margin-bottom: px2rem(20);
    .tag-content {
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 900;
      line-height: 100%; /* 14px */
      position: relative;
      z-index: 0;
      div {
        background: #d68fff;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &::after {
        content: attr(data-title);
        text-align: center;
        -webkit-text-stroke-width: px2rem(2);
        -webkit-text-stroke-color: #5f0297;
        position: absolute;
        inset: 0;
        z-index: -1;
      }
    }
  }
}
</style>

<template>
  <div :class="showBg ? 'title-block-out' : ''">
    <div
      class="title-block"
      :data-title="title"
      :style="{
        fontSize: `${$pxToRemPx(fontSize)}px`,
        lineHeight: `${$pxToRemPx(lineHeight)}px`,
        minWidth: `${$pxToRemPx(minWidth)}px`,
      }"
    >
      <div>{{ title }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  title: {
    type: String,
  },
  showBg: {
    type: Boolean,
    default: true,
  },
  fontSize: {
    type: Number,
    default: 18,
  },
  lineHeight: {
    type: Number,
    default: 35,
  },
  minWidth: {
    type: Number,
    default: 220,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.title-block-out {
  background: linear-gradient(
    90deg,
    rgba(49, 69, 203, 0) 0%,
    #5731cb 51.88%,
    rgba(49, 69, 203, 0) 100%
  );
  &::before,
  &::after {
    content: "";
    display: block;
    height: px2rem(1);
    width: 100%;
    background: linear-gradient(
      90deg,
      rgba(49, 69, 203, 0) 0%,
      rgba(134, 136, 255, 1) 51.88%,
      rgba(49, 69, 203, 0) 100%
    );
  }
}
.title-block {
  text-align: center;
  width: fit-content;
  font-family: DIN Next W1G;
  font-style: normal;
  font-weight: 900;
  position: relative;
  z-index: 0;
  div {
    text-shadow: 0 0 4px rgba(156, 107, 255, 0.33);
    -webkit-text-stroke-width: px2rem(2);
    -webkit-text-stroke-color: rgba(156, 107, 255, 0.33);
    position: absolute;
    inset: 0;
    z-index: 0;
  }
  &::after {
    content: attr(data-title);
    position: relative;
    z-index: 1;
    background: linear-gradient(
      92deg,
      #d7bfff -1.62%,
      #fff 20.17%,
      #fdfcff 55.6%,
      #d7bfff 73.42%,
      #fff 85.68%,
      #d7bfff 96.31%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>

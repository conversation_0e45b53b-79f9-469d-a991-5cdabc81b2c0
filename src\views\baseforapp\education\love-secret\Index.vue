<template>
  <div class="content">
    <header-bar :padding="false" closeType="close" fontColor="#fff" />
    <img class="title" :src="getImageUrl('loveguide.png')" />
    <div class="chat-img">
      <img :src="getImageUrl('chat.png')" />
    </div>
    <div class="chat-content">
      <div class="chat-title">
         {{ $t('education.education_8') }} ❤️ 
      </div>
      <div class="inner-box">
        {{ $t('education.education_9') }} {{ $t('education.education_10') }}
      </div>
      <div class="chat-son-title">
         🔍{{ $t('education.education_11') }}
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_12') }}
      </div>
      <div class="chat-son-title">
         💖 {{ $t('education.education_13') }}
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_14') }}
      </div>
      <div class="love-content">
        <div class="love-content-item" v-for="item in 2" :key="item">
          <div class="svg-content">
            <svg-icon icon="love" class="icon-love"></svg-icon>
            <gap :gap="4"></gap>
            <svg-icon icon="love" class="icon-love"></svg-icon>
            <gap :gap="4"></gap>
            <svg-icon icon="love" class="icon-love"></svg-icon>
          </div>
          <img v-if="item === 1" :src="getImageUrl('1-1.png')" alt="">
          <img v-if="item === 2" :src="getImageUrl('1-2.png')" alt="">
        </div>
        <img class="letter" src="@/assets/images/education/love-secret/letter.png" alt="">
      </div>
    </div>
    <div class="chat-content">
      <div class="chat-title">
        {{ $t('education.education_15') }}
      </div>
      <div class="chat-son-title">
        {{ $t('education.education_16') }} ✨
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_17') }}
      </div>
      <div class="chat-son-title">
         🌷 {{ $t('education.education_18') }}
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_19') }} 
      </div>
      <div class="chat-son-title">
         💰 {{ $t('education.education_20') }} 
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_21') }}
      </div>
      <div class="inner-box">
        <div class="inner-box-title">
          {{ $t('income.msg_exchange_success_title') }}
        </div>
         💬{{ $t('education.education_22') }}  <br>
        ⏰{{ $t('education.education_23') }} 
      </div>
      <div class="love-content">
        <div class="love-content-item" v-for="item in 2" :key="item">
          <div class="svg-content">
            <svg-icon icon="love" class="icon-love"></svg-icon>
            <gap :gap="4"></gap>
            <svg-icon icon="love" class="icon-love"></svg-icon>
            <gap :gap="4"></gap>
            <svg-icon icon="love" class="icon-love"></svg-icon>
          </div>
          <img v-if="item === 1" :src="getImageUrl('2-1.png')" alt="">
          <img v-if="item === 2" :src="getImageUrl('2-2.png')" alt="">
        </div>
        <img class="letter" src="@/assets/images/education/love-secret/letter.png" alt="">
      </div>
    </div>
    <div class="chat-content">
      <div class="chat-title">
         {{ $t('education.education_26') }} 
      </div>
      <div class="chat-son-title">
        ✨ {{ $t('education.education_27') }} 
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_28') }} 
      </div>
      <div class="chat-son-title">
        🎮 {{ $t('education.education_29') }} 
      </div>
      <div class="chat-son-text">
        - {{ $t('education.education_30') }} 
        - {{ $t('education.education_31') }} 
      </div>
      <div class="chat-son-title">
        💌 {{ $t('education.education_32') }} 
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_33') }} 
      </div>
      <div class="chat-son-title">
        👉 {{ $t('education.education_34') }} 
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_35') }} 
      </div>
      <div class="chat-son-title">
        🔔 {{ $t('education.education_36') }} 
      </div>
      <div class="chat-son-text">
        {{ $t('education.education_37') }}  ❤️
      </div>
      <div class="chat-title">
        {{ $t('education.education_38') }} 
      </div>
      <div class="love-content" style="justify-content: center;">
        <img class="center-img" :src="getImageUrl('3.png')" alt="">
        <img class="letter" src="@/assets/images/education/love-secret/letter.png" alt="">
      </div>
    </div>
    <div class="chat-content">
      <div class="chat-title">
        {{ $t('education.education_39') }} 
      </div>
      <div class="chat-son-title">
        1️⃣{{ $t('education.education_161') }} 
      </div>
      <div class="chat-son-text">
        <p>✨ {{ $t('education.education_40') }} </p>
        <p>📸 {{ $t('education.education_41') }} </p>
        <p>✅ {{ $t('education.education_42') }} </p>
        <div class="chat-son-blue-text">
          {{ $t('education.education_43') }}
        </div>
      </div>
      <div class="love-content">
        <div class="love-content-item" v-for="item in 2" :key="item">
          <div class="svg-content">
            <svg-icon icon="love" class="icon-love"></svg-icon>
            <gap :gap="4"></gap>
            <svg-icon icon="love" class="icon-love"></svg-icon>
            <gap :gap="4"></gap>
            <svg-icon icon="love" class="icon-love"></svg-icon>
          </div>
          <img v-if="item === 1" :src="getImageUrl('4-1.png')" alt="">
          <img v-if="item === 2" :src="getImageUrl('4-2.png')" alt="">
        </div>
        <img class="letter" src="@/assets/images/education/love-secret/letter.png" alt="">
      </div>
      <div class="chat-son-title">
        2️⃣{{ $t('education.education_46') }}
      </div>
      <div class="chat-son-text">
        <p>📝 {{ $t('education.education_47') }}</p>
        <p>1.{{ $t('education.education_48') }}</p>
        <p>2.{{ $t('education.education_49') }}</p>
        <p>3.{{ $t('education.education_50') }}</p>
      </div>
      <div class="double-img">
        <img :src="getImageUrl('5-1.png')" alt="">
        <img :src="getImageUrl('5-2.png')" alt="">
      </div>
      <div class="chat-son-title mt10">
        3️⃣{{ $t('education.education_51') }}
      </div>
      <div class="chat-son-text">
        <p>💬{{ $t('education.education_52') }}</p>
        <p>💡{{ $t('education.education_53') }}</p>
      </div>
      <div class="double-img">
        <img :src="getImageUrl('6-1.png')" alt="">
        <img :src="getImageUrl('6-2.png')" alt="">
      </div>
      <div class="chat-son-title mt10">
        📹{{ $t('education.education_54') }}
      </div>
      <div class="chat-son-text">
        <p>🌸{{ $t('education.education_55') }}</p>
      </div>
      <img class="modal-img" :src="getImageUrl('7.png')" alt="">
      <div class="inner-box">
        <div class="inner-box-title">
          💡{{ $t('income.msg_exchange_success_title') }}
        </div>
        {{ $t('education.education_56') }}   
      </div>
      <div class="chat-son-title">
        🔔{{ $t('education.education_58') }}
      </div>
      <div class="chat-son-text">
        <p>1.{{ $t('education.education_59') }}</p>
        <p>2.{{ $t('education.education_60') }}</p>
      </div>
      <div class="chat-son-title">
        4️⃣{{ $t('education.education_61') }}
      </div>
      <div class="chat-son-text">
        <p>🎧 {{ $t('education.education_62') }} - {{ $t('education.education_63') }}</p>
        <p>🏠 {{ $t('education.education_64') }}</p>
        <p>💡 {{ $t('education.education_65') }}：{{ $t('education.education_66') }}</p>
      </div>
    </div>
    <div class="chat-content">
      <div class="chat-title">
        {{ $t('education.education_67') }}
      </div>
      <div class="inner-box">
        {{ $t('education.education_68') }} 🌟
      </div>
      <div class="chat-son-title">
        {{ $t('education.education_69') }}
      </div>
      <div class="chat-son-text">
        <p>✅ {{ $t('education.education_70') }}：{{ $t('education.education_71') }}</p>
        <p>✅ {{ $t('education.education_72') }}：{{ $t('education.education_73') }}</p>
      </div>
      <div class="inner-box">
        <div class="inner-box-title">
          {{ $t('education.education_74') }}
        </div>
        <p>🚫 {{ $t('education.education_75') }}</p>
        <p>🚫 {{ $t('education.education_76') }}</p>
        <p>🚫 {{ $t('education.education_77') }}</p>
        <p>🚫 {{ $t('education.education_78') }}</p>
      </div>
    </div>
    <div class="bottom">
      <img class="heart" src="@/assets/images/education/love-secret/heart.png" />
      <gap :gap="6" />
      <div class="bottom-text"> {{ $t('education.education_79') }} </div>
      <gap :gap="6" />
      <img class="heart heart-back" src="@/assets/images/education/love-secret/heart.png" />
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance,ref,onMounted } from 'vue'
import { i18n } from '@/i18n/index.js'

const t = i18n.global.t
const { proxy } = getCurrentInstance()

const lang = proxy.$languageFile

function getImageUrl(name) {
  let langstr = lang
  if(lang === 'zh-CN' || lang === 'zh-TW') {
    langstr = 'en'
  }
  return new URL(
    `../../../../assets/images/education/love-secret/${langstr}/${name}`,
    import.meta.url
  ).href;
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.content {
  background: 
    url("@/assets/images/education/love-secret/bg.jpg") no-repeat top center / 100vw auto,
    linear-gradient(to bottom, transparent, #F29CFF);
  background-color: #F29CFF;
  min-height: 100vh;
  .title {
    height: px2rem(86);
    margin-top: px2rem(80);
  }
  .chat-img {
    margin-top: px2rem(220);
    padding: 0 px2rem(6);
    img {
      width: 100%;
    }
  }
  .chat-content {
    border-radius: px2rem(12);
    background: linear-gradient(149deg, #FFDAF3 6.24%, #FFF 40.52%, #FFF 60.91%, #FFD2EB 91.96%);
    box-shadow: 0px 5px 6px 0px rgba(128, 0, 89, 0.24);
    margin: px2rem(22) px2rem(20);
    padding: px2rem(17) px2rem(35);
    position: relative;
    .chat-title {
      background: linear-gradient(270deg, #78D1FB 0%, #C150FF 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
      text-shadow: 0px 2px 4px rgba(114, 0, 89, 0.20);
      -webkit-text-stroke-width: 1px;
      -webkit-text-stroke-color: #FFF;
      font-size: px2rem(18);
      font-weight: 900;
      margin-bottom: px2rem(18);
    }
    .inner-box {
      border-radius: px2rem(8);
      background: #FFF5FA;
      box-shadow: 2px 2px 6px 0px rgba(124, 30, 100, 0.18) inset;
      padding: px2rem(18) px2rem(20);
      margin: px2rem(20) px2rem(6) px2rem(14) px2rem(6);
      color: #EC6CFF;
      text-align: center;
      font-size: px2rem(12);
      font-style: normal;
      font-weight: 500;
      .inner-box-title {
        font-size: px2rem(16);
        font-weight: 700;
        margin-bottom: px2rem(2);
      }
    }
    .chat-son-title {
      border-radius: px2rem(20);
      background: #FF5EC4;
      color: #fff;
      font-size: px2rem(12);
      font-weight: 700;
      width: fit-content;
      padding: px2rem(4) px2rem(12);
      line-height: 1.1;
      display: flex;
      align-items: center;
      margin-bottom: px2rem(8);
    }
    .chat-son-text {
      color: #FF78CE;
      font-size: px2rem(12);
      font-style: normal;
      font-weight: 500;
      margin-bottom: px2rem(14);
      .chat-son-blue-text {
        color: #76A9FF;
        font-size: px2rem(12);
        font-weight: 700;
      }
    }
    .double-img {
      display: flex;
      justify-content: space-between;
      & > img {
        width: px2rem(130);
        height: px2rem(260);
      }
    }
    .modal-img {
      width: 100%;
    }
    .love-content {
      display: flex;
      justify-content: space-between;
      .love-content-item {
        .svg-content {
          display: flex;
          margin-left: px2rem(6);
          .icon-love {
            width: px2rem(20);
            height: px2rem(20);
          }
        }
        img {
          width: px2rem(130);
          height: px2rem(260);
          margin-top: px2rem(8);
        }
      }
      .letter {
        width: px2rem(66);
        height: px2rem(66);
        position: absolute;
        right: px2rem(-16);
        bottom: px2rem(-20);
      }
      .center-img {
        width: px2rem(130);
        height: px2rem(260);
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    padding: px2rem(10) px2rem(33) px2rem(40) px2rem(33);
    .bottom-text {
      color: #FFF;
      text-align: center;
      text-shadow: 
        0 2px 4px rgba(114, 0, 89, 0.20),
        /* 创建渐变描边效果 */
        -1px -1px 0 #FF41EC,
        1px -1px 0 #FF41EC,
        -1px 1px 0 #00B2FF,
        1px 1px 0 #00B2FF;
      font-size: px2rem(18);
      font-style: normal;
      font-weight: 700;
    }
    .heart {
      width: px2rem(46);
    }
    .heart-back {
      transform: rotate(45deg);
    }
  }
}
.mt10 {
  margin-top: px2rem(10);
}
</style>

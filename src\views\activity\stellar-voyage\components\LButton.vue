<template>
  <div class="l-button" :class="`type-${type}`" :style="style">
    <div class="inner">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  width: {
    type: [Number, String],
    default: 60,
  },
  height: {
    type: [Number, String],
    default: 25,
  },
  fontSize: {
    type: [Number, String],
    default: 12,
  },
  type: {
    type: String,
    default: "primary", // primary active disabled
  },
});

const style = computed(() => {
  const { width, height, fontSize } = props;
  return {
    width: typeof width === "number" ? `${proxy.$pxToRemPx(width)}px` : width,
    height:
      typeof height === "number" ? `${proxy.$pxToRemPx(height)}px` : height,
    fontSize:
      typeof fontSize === "number"
        ? `${proxy.$pxToRemPx(fontSize)}px`
        : fontSize,
  };
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.l-button {
  border-radius: px2rem(30);
  padding: px2rem(1);
  width: fit-content;
  height: fit-content;
  background: linear-gradient(
    104deg,
    #ffffff 8.33%,
    #6d74ff 39.85%,
    #ffffff 56.5%,
    #a19dff 75.95%,
    #5c5aff 125.47%
  );
  box-shadow: 0 px2rem(15) px2rem(1) 0 rgba(255, 255, 255, 0.1) inset,
    px2rem(1) px2rem(1) px2rem(2) 0 rgba(255, 255, 255, 0.4) inset;
  .inner {
    border-radius: px2rem(30);
    width: fit-content;
    height: fit-content;
    background: linear-gradient(
      104deg,
      #5eedff 8.33%,
      #3773ff 39.85%,
      #282ec5 56.5%,
      #4e3bf9 75.95%,
      #c06dff 125.47%
    );
    color: #fff;
    text-align: center;
    text-shadow: 0 0 px2rem(4) rgba(54, 0, 148, 0.25);
    font-family: Gilroy;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
}
.type-active {
  background: #fff0a8;
  .inner {
    background: linear-gradient(180deg, #fff0a8 37.72%, #ff8420 112.09%);
    color: #cb2200;
  }
}
.type-disabled {
  background: #5f57bc;
  .inner {
    background: linear-gradient(180deg, #3821a1 35.8%, #251993 70.67%);
    color: #7678ef;
    box-shadow: 0 px2rem(3) px2rem(3) 0 rgba(255, 255, 255, 0.26) inset;
  }
}
</style>

<template>
  <van-overlay
    class-name="prize-dialog"
    :show="showDialog"
    @touchmove.prevent
    z-index="10001"
  >
    <div v-if="showDialog" class="base-modal-wrapper" @click.stop>
      <div class="box">
        <div class="ap"></div>
        <div class="title">
          <TitleBlock
            :title="$t('stellar_voyage.broadcast_definition')"
            :showBg="false"
          ></TitleBlock>
        </div>
        <div class="desc">
          <p>
            <span>1. </span>
            <span>{{ $t("stellar_voyage.room_owner_broadcast") }}</span>
          </p>
          <p>
            <span>2. </span>
            <span>{{ $t("stellar_voyage.continuous_broadcast_rule") }}</span>
          </p>
        </div>
        <div class="action">
          <LButton
            :width="168"
            :height="38"
            :font-size="18"
            @click="showDialog = false"
          >
            {{ $t("stellar_voyage.ok") }}
          </LButton>
        </div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";
import TitleBlock from "./TitleBlock.vue";
import LButton from "./LButton.vue";

const showDialog = ref(false);

const handleOpen = () => {
  showDialog.value = true;
};

defineExpose({
  open: handleOpen,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.88);
}
@keyframes show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .box {
    width: px2rem(331);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    position: relative;
    border-radius: px2rem(13);
    border: px2rem(1) solid #8b8dff;
    background: linear-gradient(0deg, #5862d0 -25.4%, #140567 37.01%);
    box-shadow: 0 0 px2rem(23) 0 #1b16a9 inset;
    padding-bottom: px2rem(25);
    .ap {
      position: absolute;
      width: px2rem(184);
      height: px2rem(109);
      top: px2rem(-50);
      left: 50%;
      transform: translateX(-50%);
      background-image: url("@/assets/images/activity/stellar-voyage/ap.png");
      background-size: 100% 100%;
    }
    .title {
      margin-top: px2rem(50);
    }
    .desc {
      color: #7686ff;
      text-align: justify;
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 16.962px */
      padding: 0 px2rem(33);
    }
    .rewards {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: px2rem(17) 0;
      .reward {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: px2rem(87);
        .cover {
          width: px2rem(70);
          height: px2rem(70);
        }
        .name {
          margin-top: px2rem(12);
          width: px2rem(87);
          color: #ffa19d;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 14px */
        }
      }
    }

    .action {
      margin-top: px2rem(20);
    }
  }
}
</style>

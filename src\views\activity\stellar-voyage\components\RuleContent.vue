<template>
  <div class="rule-content">
    <div class="rule-content-desc">
      {{ $t("stellar_voyage.explorer_description") }}
    </div>
    <div class="content-block">
      <div class="cell">
        <div class="label">1</div>
        <gap :gap="14"></gap>
        <div class="content">
          {{ $t("stellar_voyage.activity_time", [timeZone]) }}
        </div>
      </div>
      <div class="cell">
        <div class="label">2</div>
        <gap :gap="14"></gap>
        <div class="content">
          {{ $t("stellar_voyage.stellar_energy_gifts_rule") }}
          <div class="box-value">
            <gender-coin :gender="11" :size="18"></gender-coin>
            <gap :gap="2"></gap>
            <span>x</span>
            <span>1</span>
            <span>=</span>
            <gap :gap="2"></gap>
            <div class="value-icon"></div>
            <gap :gap="2"></gap>
            <span>x</span>
            <span>1</span>
          </div>
        </div>
      </div>
      <div class="cell">
        <div class="label">3</div>
        <gap :gap="14"></gap>
        <div class="content">
          {{ $t("stellar_voyage.voyage_task_rewards") }}
        </div>
      </div>
      <div class="cell">
        <div class="label">4</div>
        <gap :gap="14"></gap>
        <div class="content">
          {{ $t("stellar_voyage.starfield_ranking_rewards") }}
          <div class="box-value">
            <gender-coin :gender="11" :size="18"></gender-coin>
            <gap :gap="2"></gap>
            <span>x</span>
            <span>1</span>
            <span>=</span>
            <gap :gap="2"></gap>
            <div class="value-icon"></div>
            <gap :gap="2"></gap>
            <span>x</span>
            <span>1</span>
          </div>
        </div>
      </div>
      <div class="cell">
        <div class="label">5</div>
        <gap :gap="14"></gap>
        <div class="content">
          {{ $t("stellar_voyage.feedback_prompt") }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  timeZone: {
    type: String,
    default: "UTC+8",
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rule-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  padding: 0 px2rem(22);
  padding-top: px2rem(23);
  padding-bottom: px2rem(60);

  .rule-content-desc {
    color: #9da8ff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 18px */
    width: px2rem(324);
  }

  .content-block {
    width: 100%;
    border-radius: px2rem(12);
    border: 0.8px solid rgba(135, 124, 255, 0.22);
    background: rgba(54, 59, 139, 0.3);
    padding: px2rem(20);
    margin-bottom: px2rem(16);
    margin-top: px2rem(26);
    &:last-child {
      margin-bottom: 0;
    }
    .cell {
      margin-bottom: px2rem(7);
      width: 100%;
      display: flex;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        width: px2rem(26);
        height: px2rem(26);
        flex-shrink: 0;
        background-image: url("@/assets/images/activity/stellar-voyage/step.png");
        display: flex;
        align-items: center;
        justify-content: center;
        background-size: 100% 100%;
        color: #fff;
        text-align: center;
        text-shadow: 0px 0px 6px #fff;
        font-family: "DomaineDisp";
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 700;
        line-height: 1;
      }
      .first {
        background: #b500ff;
        color: #ffdf9c;
      }
      .content {
        color: #9da8ff;
        font-family: Gilroy;
        font-size: px2rem(12);
        font-style: normal;
        font-weight: 400;
        line-height: 140%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }
}

.box-value {
  margin-top: px2rem(2);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #9da8ff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(12);
  font-style: normal;
  font-weight: 400;
  line-height: 140%; /* 16.8px */

  .value-icon {
    width: px2rem(20);
    height: px2rem(20);
    background-image: url("@/assets/images/activity/stellar-voyage/value.png");
    background-size: 100% 100%;
  }
}
</style>

<template>
  <div class="p-box">
    <div class="inner" :style="{ width: width }"></div>
  </div>
</template>
<script setup>
import { computed } from "vue";

const props = defineProps({
  max: {
    type: Number,
    default: 100,
  },
  current: {
    type: Number,
    default: 0,
  },
});

const width = computed(() => `${(props.current / props.max) * 100}%`);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.p-box {
  width: 100%;
  height: px2rem(8);
  border-radius: px2rem(4);
  border: px2rem(1) solid rgba(255, 255, 255, 0.08);
  background: #341f91;
  position: relative;
  .inner {
    position: absolute;
    top: px2rem(-1);
    left: 0;
    height: px2rem(8);
    background: linear-gradient(-90deg, #4b84ff 0%, #7640ff 50%, #9c62ff 100%);
    border-radius: px2rem(4);
  }
}

.rtl-html {
  .p-box {
    .inner {
      left: unset;
      right: 0;
    }
  }
}
</style>

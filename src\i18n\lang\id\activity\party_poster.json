{"title": "Buat Pesta Menangkan Hadiah Menarik", "intro_desc": "Fitur pesta di dalam ruangan kini sudah tersedia! 🎉🎉🎉 Kamu berkesempatan mendapatkan hadiah berikut dengan menjadi tuan rumah pesta. Kamu juga bisa mendapatkan gelar kehormatan tambahan jika mengadakan pesta antara 7.28-8.10.", "extra_title_period": "Kamu juga bisa mendapatkan gelar kehormatan tambahan jika mengadakan pesta antara 7.28-8.10.", "gift_heat_requirements": "<PERSON><PERSON><PERSON>", "room_owner_rewards": "<PERSON><PERSON> untuk Pemilik Ruang<PERSON>", "party_requirements": "Party requirement", "room_owner_rewards_duplicate": "<PERSON><PERSON> untuk Pemilik Ruang<PERSON>", "top_rewards": "<PERSON>iah Top1-3", "requirement_users_20": "*<PERSON><PERSON><PERSON> pengguna yang masuk ke ruangan mencapai 20", "requirement_gift_users_5": "*<PERSON><PERSON><PERSON> pengguna yang memberi gift mencapai 5", "requirement_heat_30k": "*Nilai popularitas ruangan mencapai 30,000", "title_support_king": "<PERSON>", "title_supporter": "Pendukung Partai", "title_charm_king": "<PERSON>", "title_fascination": "Pesona Partai", "note_weekly_limit": "Catatan: <PERSON><PERSON> bisa menerima hingga 5 kali per minggu", "extra_reward": "<PERSON><PERSON>", "qualified_parties_count": "Ju<PERSON>lah Pesta yang <PERSON>", "note_extra_title": "*Catatan: Anda bisa mendapatkan gelar kehormatan tambahan dengan menyelenggarakan pesta yang memenuhi persyaratan Hadiah 2 antara 28 Juli dan 10 Agustus. <PERSON><PERSON> akan dibagikan pada 4 Agustus dan 11 Agustus.", "play_guide_title": "Panduan Pesta Main:", "talent_party_title": "1. Pesta Bakat", "talent_mic_allocation": "Pembagian Mikrofon:", "talent_mic_desc": "Host berada di posisi mikrofon 1 (tetap), mikrofon 2–6 digunakan oleh peserta. Penonton tidak diperbolehkan menggunakan mikrofon.", "talent_competition_process": "Proses Acara:", "talent_competition_desc": "Peserta tampil menyanyi satu per satu (1–2 lagu). Host akan <PERSON> peserta, sementara penonton diminta menjaga ketenangan selama penampilan berlangsung.", "talent_interactive_voting": "Voting & Dukungan:", "talent_voting_desc": "<PERSON><PERSON><PERSON>, penonton dapat member<PERSON>n suara melalui layar umum atau mengirim hadiah untuk menambah poin peserta favorit.", "talent_promotion_elimination": "Sistem Eliminasi:", "talent_elimination_desc": "<PERSON><PERSON><PERSON> tia<PERSON> ronde, peserta dengan suara atau hadiah terendah akan tereliminasi. Peserta lainnya lanjut ke babak berikutnya hingga terpilih juara 1, 2, dan 3.", "pk_party_title": "2. Pesta PK", "pk_open_mode": "Mode PK:", "pk_mode_desc": "Bisa menggunakan PK suara terbuka atau PK tim.", "pk_process": "Jalannya PK:", "pk_process_desc": "Host akan men<PERSON><PERSON> P<PERSON>, lalu peserta menjalankan PK sesuai giliran.", "pk_result_announcement": "<PERSON><PERSON><PERSON>:", "pk_result_desc": "<PERSON><PERSON><PERSON> waktu habis, sistem secara otomatis menghitung skor. Host akan mengumumkan pemenang beserta hadiah atau hukuman yang ditentukan (misalnya pemenang menyanyi lagu pilihan, yang kalah menampilkan pertunjukan).", "pk_interesting_interaction": "<PERSON><PERSON><PERSON>:", "pk_interaction_desc": "Peserta yang kalah dapat dikenakan hukuman menarik untuk menambah keseruan acara.", "game_party_title": "3. Pesta Game", "game_mic_allocation": "Pembagian Mikrofon:", "game_mic_desc": "Mikrofon 1 untuk host, mikrofon 2–8 digunakan secara bergiliran oleh peserta dalam permainan seperti tebak gambar, sambung kata, atau siapa penyusup.", "game_process": "Proses <PERSON>:", "game_process_desc": "Host men<PERSON><PERSON><PERSON> aturan permainan dan memilih peserta yang akan naik mikrofon.", "game_rotation_mechanism": "<PERSON><PERSON><PERSON>:", "game_rotation_desc": "<PERSON>elah satu sesi permainan, peserta terbaik tetap di mikrofon, sementara peserta yang kalah digantikan oleh pemain baru.", "game_interaction_method": "Partisipasi Penonton:", "game_interaction_desc": "Penonton dapat ikut menebak atau mendukung pemain melalui layar umum, atau diundang ke mikrofon untuk ikut berpartisipasi dalam sesi tertentu.", "game_party_number": "Pesta Game", "raward_1": "Hadiah 1", "raward_2": "Hadiah 2", "raward_3": "<PERSON><PERSON>", "worth": "<PERSON><PERSON><PERSON>"}
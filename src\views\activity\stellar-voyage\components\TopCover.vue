<template>
  <div class="top-cover">
    <img v-if="image" :src="image" />
    <div v-else class="empty-avatar">
      <svg-icon icon="vector" :size="20"></svg-icon>
    </div>
    <div :class="`top${rank}-cover`"></div>
  </div>
</template>
<script setup>
const props = defineProps({
  rank: {
    type: Number,
    default: 4,
  },
  image: {
    type: String,
    default: "",
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.top-cover {
  width: 100%;
  height: 100%;
  img {
    width: px2rem(50);
    height: px2rem(50);
    border-radius: px2rem(6);
    object-fit: cover;
    z-index: 1;
  }
  .top1-cover {
    width: px2rem(74);
    height: px2rem(71);
    position: absolute;
    inset: 0;
    z-index: 2;
    background-image: url("@/assets/images/activity/stellar-voyage/top1-avatar.png");
    background-size: 100% 100%;
  }
  .top2-cover {
    width: px2rem(74);
    height: px2rem(69);
    position: absolute;
    inset: 0;
    z-index: 2;
    background-image: url("@/assets/images/activity/stellar-voyage/top2-avatar.png");
    background-size: 100% 100%;
  }
  .top3-cover {
    width: px2rem(67);
    height: px2rem(63);
    position: absolute;
    inset: 0;
    z-index: 2;
    background-image: url("@/assets/images/activity/stellar-voyage/top3-avatar.png");
    background-size: 100% 100%;
  }
  .top4-cover {
    width: px2rem(38);
    height: px2rem(38);
    position: absolute;
    inset: 0;
    z-index: 2;
    background-image: url("@/assets/images/activity/stellar-voyage/top4-avatar.png");
    background-size: 100% 100%;
  }
  .empty-avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
}
</style>

<template>
  <van-overlay
    class-name="rank-dialog"
    :show="showDialog"
    :lock-scroll="false"
    z-index="10002"
    @click="showDialog = false"
  >
    <div class="base-modal-wrapper" @click.stop="">
      <div class="bg"></div>
      <div class="ap"></div>
      <div class="top-box">
        <div class="room-cover">
          <img v-if="room?.cover" :src="room?.cover" />
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
          <div v-if="room?.rank === 1" class="room-top1-cover"></div>
          <div v-else-if="room?.rank === 2" class="room-top2-cover"></div>
          <div v-else-if="room?.rank === 3" class="room-top3-cover"></div>
          <div v-else class="room-top4-cover"></div>
        </div>
        <div class="rank-number">
          <TitleBlock
            :title="room?.rank || '-'"
            :showBg="false"
            :fontSize="18"
          ></TitleBlock>
        </div>
        <div class="value-box">
          <div class="pk-value-icon"></div>
          <gap :gap="3"></gap>
          <div class="coin-number">
            {{ formatCount(room?.coin ?? 0) }}
          </div>
        </div>
        <div class="rank-title">
          <TitleBlock
            :title="$t('stellar_voyage.contributor_ranking')"
            :showBg="false"
          ></TitleBlock>
        </div>
      </div>
      <div class="ranking-list">
        <div class="rank-item" v-for="(item, index) in list" :key="index">
          <div class="index">{{ item.rank }}</div>
          <gap :gap="8"></gap>
          <div class="avatar">
            <frame-avatar
              :avatar="item.cover"
              :frame="item.frame"
              :size="36"
            ></frame-avatar>
          </div>
          <gap :gap="8"></gap>
          <div class="nickname">
            <div>{{ item.name || "-" }}</div>
          </div>
          <gap :gap="8"></gap>
          <div class="value-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(item?.coin ?? 0) }}</div>
          </div>
          <gap :gap="16"></gap>
        </div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";
import TitleBlock from "./TitleBlock.vue";
import { formatCount } from "@/utils/util.js";
import activityApi from "@/api/activity.js";
const showDialog = ref(false);
const room = ref();
const list = ref([]);
const fetchData = async () => {
  const res = await activityApi.live_contribution_rank({
    page: 1,
    size: 9999,
    room_id: room.value.room_id,
  });
  if (res.code === 200) {
    list.value = (res.data || []).map((item) => {
      return {
        name: item.nickname || "-",
        cover: item.avatar,
        coin: item.count || 0,
        rank: item.rank,
        frame: null,
      };
    });
  }
};
const handleOpen = (roomItem) => {
  list.value = [];
  room.value = roomItem;
  if (!roomItem) {
    return;
  }
  fetchData();
  showDialog.value = true;
};
defineExpose({
  open: handleOpen,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rank-dialog {
  .base-modal-wrapper {
    width: 100%;
    height: 70%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: px2rem(25);

    position: fixed;
    bottom: 0;

    .bg {
      display: flex;
      flex-direction: column;
      position: absolute;
      inset: 0;
      &::before {
        content: "";
        width: 100%;
        height: px2rem(394);
        background-image: url("@/assets/images/activity/stellar-voyage/bg9.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        flex-shrink: 0;
      }
      &::after {
        content: "";
        width: 100%;
        height: px2rem(389);
        background-image: url("@/assets/images/activity/stellar-voyage/bg10.png");
        background-size: 100% px2rem(116);
        background-repeat: repeat-y;
      }
    }

    .ap {
      position: absolute;
      width: px2rem(206);
      height: px2rem(123);
      top: px2rem(-65);
      left: 50%;
      transform: translateX(-50%);
      background-image: url("@/assets/images/activity/stellar-voyage/ap.png");
      background-size: 100% 100%;
    }

    .top-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      .room-cover {
        width: px2rem(74);
        height: px2rem(71);
        margin-top: px2rem(43);
        z-index: 10;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: px2rem(28);
        img {
          width: px2rem(50);
          height: px2rem(50);
          border-radius: px2rem(6);
          object-fit: cover;
          z-index: 1;
        }
        .empty-avatar {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
        }
        .room-top1-cover {
          width: px2rem(74);
          height: px2rem(71);
          position: absolute;
          inset: 0;
          z-index: 2;
          background-image: url("@/assets/images/activity/stellar-voyage/top1-avatar.png");
          background-size: 100% 100%;
        }
        .room-top2-cover {
          width: px2rem(74);
          height: px2rem(69);
          position: absolute;
          inset: 0;
          z-index: 2;
          background-image: url("@/assets/images/activity/stellar-voyage/top2-avatar.png");
          background-size: 100% 100%;
        }
        .room-top3-cover {
          width: px2rem(74);
          height: px2rem(74);
          position: absolute;
          inset: 0;
          top: px2rem(-6);
          left: px2rem(0);
          z-index: 2;
          background-image: url("@/assets/images/activity/stellar-voyage/top3-avatar.png");
          background-size: 100% 100%;
        }
        .room-top4-cover {
          width: px2rem(70);
          height: px2rem(70);
          position: absolute;
          inset: 0;
          z-index: 2;
          background-image: url("@/assets/images/activity/stellar-voyage/top4-avatar.png");
          background-size: 100% 100%;
        }
      }

      .rank-number {
        position: relative;
        z-index: 9;
        margin-top: px2rem(6);
      }
      .value-box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        z-index: 2;
        border-radius: px2rem(20);
        border: 0.523px solid rgba(161, 132, 227, 0.8);
        background: linear-gradient(
          137deg,
          rgba(38, 30, 149, 0.4) -0.32%,
          rgba(51, 0, 169, 0.4) 44.39%,
          rgba(46, 109, 255, 0.4) 94.8%
        );
        box-shadow: 0 px2rem(-2) px2rem(-2) 0 rgba(74, 63, 230, 0.2) inset;
        height: px2rem(26);
        min-width: px2rem(94);

        .pk-value-icon {
          width: px2rem(18);
          height: px2rem(18);
          background-image: url("@/assets/images/activity/stellar-voyage/value.png");
          background-size: 100% 100%;
        }
        .coin-number {
          color: #fff;
          text-shadow: 0 0 3.663px #6a00ff;
          font-family: "DIN Next W1G";
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 12.82px */
        }
      }
      .rank-title {
        margin-top: px2rem(15);
      }
    }

    .ranking-list {
      flex-grow: 1;
      overflow-y: auto;
      padding: 0 px2rem(15);
      margin-top: px2rem(10);
      width: 100%;
      position: relative;
      z-index: 999;
      .rank-item {
        position: relative;
        display: flex;
        width: 100%;
        height: px2rem(70);
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        border-radius: px2rem(8);
        background: rgba(255, 0, 0, 0.14);
        margin-bottom: px2rem(6);

        border-radius: px2rem(9);
        border: px2rem(1.25) solid rgba(255, 255, 255, 0.28);
        background: linear-gradient(
          90deg,
          rgba(15, 12, 78, 0.29) 0%,
          rgba(2, 0, 43, 0.29) 100%
        );
        .index {
          width: px2rem(28);
          color: #fff;
          text-align: right;
          font-family: "DIN Next W1G";
          font-size: px2rem(17);
          font-style: italic;
          font-weight: 750;
          line-height: normal;
          position: relative;
          flex-shrink: 0;
        }
        .avatar {
          display: flex;
          width: px2rem(46);
          height: px2rem(46);
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          position: relative;
        }
        .nickname {
          color: var(---White, #fff);

          /* T13/R */
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 400;
          line-height: 124%; /* 16.12px */
          flex-grow: 1;
        }
        .value-box {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          position: relative;
          z-index: 2;

          .pk-value-icon {
            width: px2rem(18);
            height: px2rem(18);
            background-image: url("@/assets/images/activity/stellar-voyage/value.png");
            background-size: 100% 100%;
          }
          .coin-number {
            color: #fff;
            text-shadow: 0 0 3.663px #6a00ff;
            font-family: "DIN Next W1G";
            font-size: px2rem(15);
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 12.82px */
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="user-ranking-block">
    <div>
      <div class="box-title">
        <TitleBlock
          :title="$t('stellar_voyage.stellar_energy_gifts')"
        ></TitleBlock>
      </div>
      <div class="box-desc">
        {{ $t("stellar_voyage.gift_count_rule") }}
      </div>
      <div class="box-value">
        <gender-coin :gender="11" :size="18"></gender-coin>
        <gap :gap="2"></gap>
        <span>x</span>
        <span>1</span>
        <span>=</span>
        <gap :gap="2"></gap>
        <div class="value-icon"></div>
        <gap :gap="2"></gap>
        <span>x</span>
        <span>1</span>
      </div>
      <div class="rewards">
        <div class="reward" v-for="prize in activityInfo.gifts || []">
          <div class="cover">
            <PrizeBox :rewards="prize"></PrizeBox>
          </div>
          <div class="coin-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(prize?.coin ?? 0) }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="rank">
      <Ranking></Ranking>
    </div>
  </div>
</template>
<script setup>
import Ranking from "./Ranking.vue";
import PrizeBox from "./PrizeBox.vue";
import TitleBlock from "./TitleBlock.vue";
import { t } from "@/i18n/index.js";
import { formatCount } from "@/utils/util.js";

const props = defineProps({
  activityInfo: {
    type: Object,
    default: () => ({}),
  },
  rewordInfo: {
    type: Object,
    default: () => ({}),
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.user-ranking-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  margin-top: px2rem(8);

  .desc {
    display: inline-flex;
    padding: px2rem(10);
    margin-top: px2rem(12);
    width: px2rem(314);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(8);
    border: 0.5px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.16);
    color: #cb8ffe;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .rank-count-down {
    margin-top: px2rem(37);
  }

  .reward-box {
    width: 100%;
    margin-top: px2rem(52);
  }

  .box-desc {
    width: px2rem(262);
    margin-top: px2rem(2);
    color: rgba(235, 157, 255, 0.65);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .box-number {
    margin-top: px2rem(4);
    text-align: center;
    text-shadow: 0px 0px px2rem(13) rgba(255, 195, 0, 0.7);
    font-family: Gilroy;
    font-size: px2rem(36);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    background: linear-gradient(
      91deg,
      #ffc300 33.4%,
      #fff 42.75%,
      #fc0 50.8%,
      #fff 58.39%,
      #ffae00 66.64%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    .qa {
      width: px2rem(16);
      height: px2rem(16);
      margin-top: px2rem(-8);
      background-image: url("@/assets/images/activity/stellar-voyage/qa.png");
      background-size: 100% 100%;
    }
  }

  .box-title {
    margin-top: px2rem(27);
    display: flex;
    justify-content: center;
  }

  .box-desc {
    margin: 0 auto;
    margin-top: px2rem(14);
    color: #9da8ff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 400;
    line-height: 140%; /* 16.8px */
    width: px2rem(271);
  }

  .box-value {
    margin-top: px2rem(2);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #9da8ff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 400;
    line-height: 140%; /* 16.8px */

    .value-icon {
      width: px2rem(20);
      height: px2rem(20);
      background-image: url("@/assets/images/activity/stellar-voyage/value.png");
      background-size: 100% 100%;
    }
  }

  .rewards {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: px2rem(17);
    margin-top: px2rem(20);
    .reward {
      display: flex;
      align-items: center;
      flex-direction: column;
      width: px2rem(87);
      .cover {
        width: px2rem(70);
        height: px2rem(70);
      }
      .name {
        margin-top: px2rem(12);
        width: px2rem(87);
        color: #ffa19d;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 14px */
      }
    }
    .coin-box {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: px2rem(11);
      .pk-value-icon {
        width: px2rem(18);
        height: px2rem(18);
        background-image: url("@/assets/images/activity/stellar-voyage/value.png");
        background-size: 100% 100%;
      }
      .coin-number {
        color: #fff;
        font-family: "DIN Next W1G";
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 700;
        line-height: 100%; /* 14px */
      }
    }
  }

  .rank {
    width: 100%;
    margin-top: px2rem(37);
  }
}

.rtl-html {
  .top4-tag {
    margin-right: px2rem(13) !important;
    margin-left: px2rem(0) !important;
  }
}
</style>

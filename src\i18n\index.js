import { createI18n } from "vue-i18n";
import { Locale } from "vant";
import { getUrlKey } from "@/utils/util.js";

import enUS from "vant/es/locale/lang/en-US";

const LanguageList = {
  ChineseSimple: "zh-CN", // 简体中文
  ChineseTraditional: "zh-TW", // 繁体中文-中国台湾
  ChineseHongKong: "zh-HK", // 繁体中文-中国香港
  English: "en", // 英文
  Arabic: "ar", // 阿拉伯语
  Turkey: "tr", // 土耳其语
  Portugal: "pt", // 葡萄牙语
  Indonesia: "id", // 印尼语
  Spanish: "es", // 西班牙语
};

// 翻译文件映射
const languageFileMap = {
  "zh-CN": "zh_cn",
  "zh-TW": "zh_tw",
  "zh-HK": "zh_tw",
  en: "en",
  ar: "ar",
  tr: "tr",
  pt: "pt",
  id: "id",
  es: "es",
};

// 所有语言列表
const langList = Object.values(LanguageList);
const messages = {};
const modules = import.meta.glob("./lang/**/*.json", {
  import: "default",
  eager: true,
});

// 非dev环境 全局替换翻译中的特殊字符
const replaceSpecialChar = (e) => {
  if (["development"].includes(process.env.VITE_PROGRESS_ENV)) return e;
  let str = JSON.stringify(e);
  str = str.replace(/(?!{')@(?!'})/g, `\@`);
  return JSON.parse(str);
};

// 新增了页面后在下面对象里增加一条即可，多语言自动匹配
langList.forEach((lang) => {
  const baseLang = {
    common: modules[`./lang/${languageFileMap[lang]}/common.json`],
    pageTitle: modules[`./lang/${languageFileMap[lang]}/pageTitle.json`],
    recharge: modules[`./lang/${languageFileMap[lang]}/recharge.json`],
    help: modules[`./lang/${languageFileMap[lang]}/help.json`],
    income: modules[`./lang/${languageFileMap[lang]}/income.json`],
    guild: modules[`./lang/${languageFileMap[lang]}/guild.json`],
    withdraw: modules[`./lang/${languageFileMap[lang]}/withdraw.json`],
    base_total: modules[`./lang/${languageFileMap[lang]}/total.json`],
    game: modules[`./lang/${languageFileMap[lang]}/game.json`],
    anchor: modules[`./lang/${languageFileMap[lang]}/anchor.json`],
    anchor_week:
      modules[`./lang/${languageFileMap[lang]}/activity/anchor_week.json`],
    wheel_lottery:
      modules[`./lang/${languageFileMap[lang]}/activity/wheel_lottery.json`],
    dealer_coin: modules[`./lang/${languageFileMap[lang]}/dealerCoin.json`],
    ranking: modules[`./lang/${languageFileMap[lang]}/ranking.json`],
    rules: modules[`./lang/${languageFileMap[lang]}/rules.json`],
    promoter_star:
      modules[`./lang/${languageFileMap[lang]}/activity/promoter_star.json`],
    invite: modules[`./lang/${languageFileMap[lang]}/invite.json`],
    recharge_ranking:
      modules[`./lang/${languageFileMap[lang]}/activity/recharge_ranking.json`],
    singer: modules[`./lang/${languageFileMap[lang]}/activity/singer.json`],
    egypt:
      modules[
        `./lang/${languageFileMap[lang]}/activity/egypt_national_day.json`
      ],
    pk724: modules[`./lang/${languageFileMap[lang]}/activity/pk724.json`],
    pk_king: modules[`./lang/${languageFileMap[lang]}/activity/pk_king.json`],
    party: modules[`./lang/${languageFileMap[lang]}/activity/party.json`],
    stellar_voyage:
      modules[`./lang/${languageFileMap[lang]}/activity/stellar_voyage.json`],
    party_poster:
      modules[`./lang/${languageFileMap[lang]}/activity/party_poster.json`],
    education: modules[`./lang/${languageFileMap[lang]}/education.json`],
  };
  messages[lang] = replaceSpecialChar(baseLang);
});

const i18n = createI18n({
  locale: "en",
  fallbackLocale: "en",
  messages,
});

// 设置当前语言
// 先从app方法获取存入storage，再优先取路由参数
const checkLang = () => {
  const entryLang = getUrlKey("lang");

  // 默认值优先取缓存，避免跳转外部链接返回后取不到lang
  const defaultLang = localStorage.getItem("siya_lang") || i18n.global.locale;
  // 入参语言字段
  const lang = entryLang || defaultLang;
  i18n.global.locale = lang;
  localStorage.setItem("siya_lang", lang);
  if (lang !== "zh-CN") Locale.use("en", enUS);
  console.log(
    `当前语言:${lang}, defaultLang:${defaultLang}, entryLang:${entryLang}`
  );

  let languageFileLang = languageFileMap[lang];
  // if (languageFileLang === "zh_cn") languageFileLang = "zh_tw";
  return languageFileLang;
};

// 获取当前语言
const getLang = () => languageFileMap[i18n.global.locale];

// 设置页面标题
const setPageTitle18n = (pageName) => {
  const config = i18n.global.messages[i18n.global.locale].pageTitle;
  const defaultTitle = config.base;
  document.title = config[pageName] || defaultTitle;
};

// 重新设置页面文字方向
const resetDocumentRtl = () => {
  const locale = getLang();
  const rightLang = ["ar"].includes(locale);
  document.documentElement.setAttribute("dir", rightLang ? "rtl" : "ltr");
  document.documentElement.setAttribute(
    "class",
    rightLang ? `rtl-html lang-${locale}` : `lang-${locale}`
  );
};

const t = i18n.global.t;

export {
  t,
  i18n,
  langList,
  checkLang,
  getLang,
  resetDocumentRtl,
  setPageTitle18n,
  languageFileMap,
};

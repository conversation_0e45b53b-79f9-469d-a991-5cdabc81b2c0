<template>
  <div class="rule-rewards">
    <div class="rule-rewards-tabs">
      <div
        class="rule-rewards-tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        <span>{{ $t("stellar_voyage.voyage_tasks") }}</span>
        <LButton
          v-if="current === 0"
          class="tab-bg"
          height="100%"
          width="100%"
        ></LButton>
      </div>
      <gap :gap="4"></gap>
      <div
        class="rule-rewards-tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
      >
        <span>{{ $t("stellar_voyage.starfield_rankings") }}</span>
        <LButton
          v-if="current === 1"
          class="tab-bg"
          height="100%"
          width="100%"
        ></LButton>
      </div>
    </div>
    <div class="rule-rewards-block">
      <div v-if="current === 0" class="rewards">
        <template v-for="(item, index) in taskRewards">
          <div class="reward">
            <div class="prize">
              <PrizeBox :rewards="item"></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <template v-if="current === 1">
        <div class="top1-title"></div>
        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.room_owner_rewards')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
          <div class="king"></div>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top1?.owner || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.top_1_3_contributors')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top1?.contribute || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="top2-title mt-22"></div>

        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.room_owner_rewards')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
          <div class="king"></div>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top2?.owner || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.top_1_3_contributors')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top2?.contribute || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="top3-title mt-22"></div>
        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.room_owner_rewards')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
          <div class="king"></div>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top3?.owner || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.top_1_3_contributors')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top3?.contribute || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="top410-title mt-22"></div>
        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.room_owner_rewards')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
          <div class="king"></div>
        </div>

        <div class="rewards mt-22">
          <template v-for="(item, index) in rewordInfo?.top410?.owner || []">
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>

        <div class="mt-22 reward-title">
          <gap :gap="12"></gap>
          <TitleBlock
            :title="$t('stellar_voyage.top_1_3_contributors')"
            :showBg="false"
            :fontSize="14"
            :minWidth="0"
          ></TitleBlock>
          <gap :gap="12"></gap>
        </div>

        <div class="rewards mt-22">
          <template
            v-for="(item, index) in rewordInfo?.top410?.contribute || []"
          >
            <div class="reward">
              <div class="prize">
                <PrizeBox :rewards="item"></PrizeBox>
              </div>
              <div class="prize-name">{{ item.reward_name }}</div>
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
import { computed, ref } from "vue";
import PrizeBox from "./PrizeBox.vue";
import LButton from "./LButton.vue";
import TitleBlock from "./TitleBlock.vue";
const props = defineProps({
  activityInfo: {
    type: Object,
    default: () => ({}),
  },
  rewordInfo: {
    type: Object,
    default: () => ({}),
  },
});
const current = ref(0);
const taskRewards = computed(() => {
  return [
    ...props.rewordInfo?.join_5_minute,
    ...props.rewordInfo?.live_1_day,
    ...props.rewordInfo?.live_2_day,
    ...props.rewordInfo?.live_3_day,
    ...props.rewordInfo?.live_4_day,
    ...props.rewordInfo?.live_5_day,
    ...props.rewordInfo?.live_6_day,
    ...props.rewordInfo?.live_7_day,
    ...props.rewordInfo?.receive_1_gift,
    ...props.rewordInfo?.room_receive_50k,
    ...props.rewordInfo?.room_receive_80k,
    ...props.rewordInfo?.room_receive_150k,
    ...props.rewordInfo?.room_receive_250k,
    ...props.rewordInfo?.room_receive_350k,
    ...props.rewordInfo?.room_receive_500k,
    ...props.rewordInfo?.send_1_gift,
  ];
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.rule-rewards {
  margin-top: px2rem(12);
  padding-bottom: px2rem(30);
  .rule-rewards-tabs {
    height: px2rem(32);
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
    margin: 0 px2rem(18);
    border-radius: px2rem(50);
    border: px2rem(1) solid #342696;
    background: rgba(133, 110, 236, 0.19);
    .rule-rewards-tab {
      width: 50%;
      height: px2rem(32);
      padding: 0 px2rem(3);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      position: relative;

      color: #9893e2;
      text-align: center;
      text-shadow: 0 px2rem(2) px2rem(3) rgba(0, 0, 0, 0.34);
      font-family: "DIN Next W1G";
      font-size: px2rem(11);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 13.64px */

      .tab-bg {
        position: absolute;
        inset: 0;
      }

      span {
        position: relative;
        z-index: 2;
      }
    }

    .rule-rewards-tab.active {
      span {
        color: #fff;
      }
    }
  }

  .rule-rewards-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: px2rem(41);
  }
}

.rewards {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: px2rem(281);
  flex-wrap: wrap;
  gap: px2rem(10);

  .reward {
    width: px2rem(87);
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;

    .prize {
      width: px2rem(70);
      height: px2rem(70);
    }

    .prize-name {
      color: #fff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 14px */
      margin-top: px2rem(12);
      width: 100%;
      height: px2rem(24);
    }
  }
}

@for $i from 1 through 7 {
  $values: 16, 22, 28, 34, 42, 50, 60;
  $mt: nth($values, $i);
  .mt-#{$mt} {
    margin-top: px2rem($mt);
  }
}

.top1-title {
  width: px2rem(60);
  height: px2rem(22);
  background-image: url("@/assets/images/activity/stellar-voyage/top1-title.png");
  background-size: 100% 100%;
}

.top2-title {
  width: px2rem(60);
  height: px2rem(22);
  background-image: url("@/assets/images/activity/stellar-voyage/top2-title.png");
  background-size: 100% 100%;
}

.top3-title {
  width: px2rem(60);
  height: px2rem(22);
  background-image: url("@/assets/images/activity/stellar-voyage/top3-title.png");
  background-size: 100% 100%;
}

.top410-title {
  width: px2rem(68);
  height: px2rem(22);
  background-image: url("@/assets/images/activity/stellar-voyage/top410-title.png");
  background-size: 100% 100%;
}

.reward-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  &::before,
  &::after {
    content: "";
    width: px2rem(36);
    height: px2rem(2);
    background-image: url("@/assets/images/activity/stellar-voyage/line.png");
    background-size: 100% 100%;
  }
  &::after {
    transform: scaleX(-1);
  }
  .king {
    width: px2rem(20);
    height: px2rem(14);
    background-image: url("@/assets/images/activity/stellar-voyage/king.png");
    background-size: 100% 100%;
    position: absolute;
    top: px2rem(-5);
    left: 50%;
    transform: translateX(-50%);
  }
}

.rtl-html {
  .reward-title {
    &::after {
      transform: scaleX(1);
    }
    &::before {
      transform: scaleX(-1);
    }
  }
}
</style>
